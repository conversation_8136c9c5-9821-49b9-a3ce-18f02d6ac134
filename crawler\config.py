# -*- coding: utf-8 -*-
"""
Configuration settings for the crawler
"""
import logging
import os

# ==================== 日志配置 ====================
def setup_logging():
    """设置日志配置"""
    logging.basicConfig(
        level=logging.INFO,
        format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        handlers=[
            logging.FileHandler('google_search.log'),
            logging.StreamHandler()
        ]
    )

# ==================== 爬虫配置 ====================
class CrawlerConfig:
    """爬虫配置类"""
    
    # 请求配置
    REQUEST_TIMEOUT = 10
    MAX_RETRIES = 3
    
    # 延迟配置
    DELAYS = {
        'retry': (1, 2),        # 重试间隔
        'page': (1, 3),         # 页面间隔  
        'query': (0.5, 1.5),    # 查询间隔
        'batch': (2, 5)         # 批次间隔
    }
    
    # User-Agent池
    USER_AGENTS = [
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/132.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Macintosh; Intel Mac OS X 10_15_7) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36',
        'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/131.0.0.0 Safari/537.36',
        'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/133.0.0.0 Safari/537.36'
    ]
    
    # 请求头模板
    BASE_HEADERS = {
        'accept': 'text/html,application/xhtml+xml,application/xml;q=0.9,image/avif,image/webp,image/apng,*/*;q=0.8,application/signed-exchange;v=b3;q=0.7',
        'accept-language': 'zh-CN,zh;q=0.9',
        'downlink': '10',
        'priority': 'u=0, i',
        'rtt': '50',
        'sec-ch-prefers-color-scheme': 'dark',
        'sec-ch-ua': '"Not(A:Brand";v="99", "Google Chrome";v="133", "Chromium";v="133"',
        'sec-ch-ua-arch': '"x86"',
        'sec-ch-ua-bitness': '"64"',
        'sec-ch-ua-form-factors': '"Desktop"',
        'sec-ch-ua-full-version': '"133.0.6943.142"',
        'sec-ch-ua-full-version-list': '"Not(A:Brand";v="99.0.0.0", "Google Chrome";v="133.0.6943.142", "Chromium";v="133.0.6943.142"',
        'sec-ch-ua-mobile': '?0',
        'sec-ch-ua-model': '""',
        'sec-ch-ua-platform': '"Windows"',
        'sec-ch-ua-platform-version': '"15.0.0"',
        'sec-ch-ua-wow64': '?0',
        'sec-fetch-dest': 'document',
        'sec-fetch-mode': 'navigate',
        'sec-fetch-site': 'none',
        'sec-fetch-user': '?1',
        'upgrade-insecure-requests': '1',
        'x-browser-channel': 'stable',
        'x-browser-copyright': 'Copyright 2025 Google LLC. All rights reserved.',
        'x-browser-year': '2025',
    }

# ==================== 代理配置 ====================
class ProxyConfig:
    """代理配置类"""
    
    # 代理API配置
    PROXY_APIS = {
        'rola': {
            'url': "http://list.rola.info:8088/user_get_ip_list?token=xD3WdIuf5zl2ragb1721654350028&qty={num}&country=&state=&city=&time={minute}&format=txt&protocol=http&filter=1",
            'num': 5,
            'minute': 5
        },
        'qg': {
            'url': "https://overseas.proxy.qg.net/get?key=A1LGFVXJ&num={num}&area=990100&isp=&format=txt&seq=\r\n&distinct=false",
            'num': 5
        }
    }
    
    # 代理更新间隔（秒）
    UPDATE_INTERVAL = 120
    
    # 代理切换阈值
    MAX_ERRORS_BEFORE_SWITCH = 3

# ==================== Cookie配置 ====================
class CookieConfig:
    """Cookie配置类"""

    # Cookie文件夹
    COOKIE_DIR = 'cookies'

    # 必需的Cookie字段
    REQUIRED_COOKIES = ['AEC', 'NID']

    # 浏览器配置
    BROWSER_CONFIG = {
        'timeout': 30000,
        'args': [
            '--disable-blink-features=AutomationControlled',
            '--disable-features=IsolateOrigins,site-per-process',
            '--no-sandbox',
            '--disable-setuid-sandbox',
        ]
    }

    # 随机搜索词（用于获取Cookie）
    SEARCH_QUERIES = [
        "what is LLM", "news headlines", "best restaurants near me",
        "how to learn python", "upcoming movies", "stock market news",
        "healthy recipes", "travel destinations", "latest technology trends",
        "home workout routines", "best coffee shops", "popular music tracks",
        "chinese food", "japanese food", "italian food", "what is popmart",
        "how to learn japanese", "agentic rag", "llm agent", "graphrag"
    ]

# ==================== Cookie池配置 ====================
class CookiePoolConfig:
    """Cookie池配置类"""

    # Cookie池大小（维护n个Cookie）
    POOL_SIZE = 5

    # Cookie失败阈值（失败m次后删除）
    MAX_FAILURES = 3

    # 查询重试时最多尝试的Cookie数量（最多k个Cookie）
    MAX_RETRY_COOKIES = None  # None表示尝试所有可用Cookie

    # Cookie池存储目录
    POOL_DIR = 'cookie_pool'

    # Cookie池状态文件
    POOL_STATUS_FILE = 'pool_status.json'

    # Cookie自动刷新间隔（秒）
    AUTO_REFRESH_INTERVAL = 3600  # 1小时

    # 池维护配置
    MIN_ACTIVE_COOKIES = 2  # 最少保持的活跃Cookie数量
    MAX_POOL_SIZE = 10      # 池的最大容量
