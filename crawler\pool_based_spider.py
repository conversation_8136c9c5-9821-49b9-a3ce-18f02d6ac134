# -*- coding: utf-8 -*-
"""
基于Cookie池的Google搜索爬虫
实现Cookie池管理和自动重试机制
"""

import time
import random
import logging
import requests
from lxml import etree
from typing import Dict, List, Optional

from config import CrawlerConfig, CookiePoolConfig
from cookie_pool import <PERSON>iePool, CookieInfo
from google_spider import GoogleSearcher
from proxy_manager import SmartProxyManager

logger = logging.getLogger(__name__)


class PoolBasedGoogleSpider(GoogleSearcher):
    """基于Cookie池的Google搜索爬虫"""
    
    def __init__(self, thread_id=0, headless=True, use_proxy=False, use_cookie_pool=True):
        """
        初始化爬虫
        
        Args:
            thread_id: 线程ID
            headless: 是否无头模式
            use_proxy: 是否使用代理
            use_cookie_pool: 是否使用Cookie池
        """
        self.thread_id = thread_id
        self.use_proxy = use_proxy
        self.use_cookie_pool = use_cookie_pool
        
        if use_cookie_pool:
            # 使用Cookie池
            self.cookie_pool = CookiePool()
            self.cookie_manager = None
            logger.info(f"[线程 {thread_id}] 使用Cookie池模式")
        else:
            # 使用传统单Cookie模式
            super().__init__(thread_id, headless, use_proxy)
            self.cookie_pool = None
            logger.info(f"[线程 {thread_id}] 使用单Cookie模式")
        
        # 初始化代理管理器
        if use_proxy:
            self.proxy_manager = SmartProxyManager(num=10, minute=5)
        else:
            self.proxy_manager = None
        
        # 初始化HTTP会话
        self.session = requests.Session()
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,
            pool_maxsize=20,
            max_retries=3
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
    
    def search_page_with_pool(self, keyword, page=1, proxy=None, error_count=0):
        """
        使用Cookie池进行搜索（带自动重试和Cookie轮换）
        
        Args:
            keyword: 搜索关键词
            page: 页码
            proxy: 代理
            error_count: 错误计数
            
        Returns:
            dict: 搜索结果
        """
        if not self.use_cookie_pool:
            # 回退到原始方法
            return super().search_page(keyword, page, CrawlerConfig.MAX_RETRIES, proxy, error_count)
        
        max_retry_cookies = CookiePoolConfig.MAX_RETRY_COOKIES
        if max_retry_cookies is None:
            max_retry_cookies = len(self.cookie_pool.get_active_cookies())
        
        tried_cookies = set()
        
        logger.info(f"[线程 {self.thread_id}] 开始搜索: {keyword}, 页码: {page}")
        
        # 尝试不同的Cookie
        for cookie_attempt in range(max_retry_cookies):
            # 获取可用Cookie
            cookie_info = self.cookie_pool.get_available_cookie()
            
            if not cookie_info or cookie_info.id in tried_cookies:
                logger.warning(f"[线程 {self.thread_id}] 没有更多可用Cookie")
                break
            
            tried_cookies.add(cookie_info.id)
            
            logger.debug(f"[线程 {self.thread_id}] 尝试Cookie: {cookie_info.id[:8]}...")
            
            # 使用当前Cookie进行搜索
            result = self._search_with_cookie(keyword, page, cookie_info, proxy)
            
            if result['success']:
                # 搜索成功
                self.cookie_pool.mark_cookie_success(cookie_info.id)
                logger.info(f"[线程 {self.thread_id}] 搜索成功，使用Cookie: {cookie_info.id[:8]}...")
                
                return {
                    'results': result['results'],
                    'has_next': result['has_next'],
                    'proxy': proxy,
                    'error_count': 0,
                    'cookie_id': cookie_info.id
                }
            else:
                # 搜索失败，标记Cookie
                self.cookie_pool.mark_cookie_failed(cookie_info.id)
                logger.warning(f"[线程 {self.thread_id}] Cookie失败: {cookie_info.id[:8]}..., 原因: {result.get('error', 'Unknown')}")
                
                # 添加重试延迟
                if cookie_attempt < max_retry_cookies - 1:
                    delay = random.uniform(*CrawlerConfig.DELAYS['retry'])
                    time.sleep(delay)
        
        # 所有Cookie都失败了
        logger.error(f"[线程 {self.thread_id}] 所有Cookie都失败，搜索失败: {keyword}")
        
        return {
            'results': [],
            'has_next': False,
            'proxy': proxy,
            'error_count': error_count + 1,
            'cookie_id': None
        }
    
    def _search_with_cookie(self, keyword, page, cookie_info: CookieInfo, proxy=None):
        """
        使用指定Cookie进行单次搜索
        
        Args:
            keyword: 搜索关键词
            page: 页码
            cookie_info: Cookie信息
            proxy: 代理
            
        Returns:
            dict: 搜索结果
        """
        try:
            # 构建搜索参数（与GoogleSearcher保持一致）
            params = {
                'q': keyword,
                'oq': keyword,
                'start': (page - 1) * 10,  # Google每页10个结果
                'gs_lcrp': '',
                'sourceid': 'chrome',
                'ie': 'UTF-8',
            }
            
            # 设置请求头
            headers = CrawlerConfig.BASE_HEADERS.copy()
            headers['user-agent'] = random.choice(CrawlerConfig.USER_AGENTS)
            
            # 设置代理
            proxy_server = None
            if proxy:
                proxy_server = {
                    'http': f'http://{proxy}',
                    'https': f'http://{proxy}'
                }
            
            # 发送请求
            start_time = time.time()
            response = self.session.get(
                'https://www.google.com/search',
                headers=headers,
                cookies=cookie_info.cookies,
                params=params,
                timeout=CrawlerConfig.REQUEST_TIMEOUT,
                proxies=proxy_server
            )
            
            response_time = time.time() - start_time
            
            if response.status_code == 200:
                # 更新代理统计
                if self.proxy_manager and proxy:
                    self.proxy_manager.update_proxy_stats(proxy, True, response_time)
                
                # 解析页面
                page_content = etree.HTML(response.text)
                
                # 检查是否被封禁
                if self._is_blocked_page(response.text):
                    logger.warning(f"[线程 {self.thread_id}] 检测到封禁页面")
                    return {
                        'success': False,
                        'error': 'blocked',
                        'results': [],
                        'has_next': False
                    }
                
                # 提取搜索结果
                results = self._extract_search_results(page_content)
                
                # 检查是否有下一页
                has_next = len(page_content.xpath('//a[@id="pnnext"]')) > 0
                
                return {
                    'success': True,
                    'results': results,
                    'has_next': has_next
                }
            
            elif response.status_code == 429:
                logger.warning(f"[线程 {self.thread_id}] 请求被限制，状态码: 429")
                return {
                    'success': False,
                    'error': 'rate_limited',
                    'results': [],
                    'has_next': False
                }
            
            else:
                logger.warning(f"[线程 {self.thread_id}] 请求返回状态码: {response.status_code}")
                return {
                    'success': False,
                    'error': f'http_{response.status_code}',
                    'results': [],
                    'has_next': False
                }
        
        except Exception as e:
            logger.error(f"[线程 {self.thread_id}] 搜索请求异常: {e}")
            
            # 更新代理统计（失败）
            if self.proxy_manager and proxy:
                self.proxy_manager.update_proxy_stats(proxy, False, 10.0)
            
            return {
                'success': False,
                'error': str(e),
                'results': [],
                'has_next': False
            }
    
    def search_page(self, keyword, page=1, max_retries=None, proxy=None, error_count=0):
        """
        搜索页面（兼容原接口）

        Args:
            keyword: 搜索关键词
            page: 页码
            max_retries: 最大重试次数（在池模式下忽略）
            proxy: 代理
            error_count: 错误计数

        Returns:
            dict: 搜索结果
        """
        if self.use_cookie_pool:
            return self.search_page_with_pool(keyword, page, proxy, error_count)
        else:
            # GoogleSearcher的search_page方法参数：(keyword, page_num, max_retries, proxy, error_count)
            # 注意：page_num从0开始，所以需要减1
            return super().search_page(keyword, page-1, max_retries or CrawlerConfig.MAX_RETRIES, proxy, error_count)
    
    def search_all_pages(self, keyword, max_pages=None, max_errors_before_proxy_change=5):
        """
        搜索所有页面（重写以支持Cookie池）

        Args:
            keyword: 搜索关键词
            max_pages: 最大页数
            max_errors_before_proxy_change: 更换代理前的最大错误数

        Returns:
            list: 所有搜索结果
        """
        if not self.use_cookie_pool:
            # 回退到原始方法
            return super().search(keyword, max_pages, max_errors_before_proxy_change)

        all_results = []
        page_num = 1
        has_next = True
        error_count = 0

        # 获取初始代理
        proxy = None
        if self.proxy_manager:
            proxy = self.proxy_manager.get_best_proxy()

        logger.info(f"[线程 {self.thread_id}] 开始搜索关键词: {keyword}")

        while has_next and (max_pages is None or page_num <= max_pages):
            logger.info(f"[线程 {self.thread_id}] 搜索第 {page_num} 页")

            # 搜索当前页
            page_result = self.search_page(keyword, page_num, None, proxy, error_count)

            # 更新代理和错误计数
            proxy = page_result['proxy']
            error_count = page_result['error_count']

            # 如果有结果，添加到总结果中
            if page_result['results']:
                all_results.extend(page_result['results'])
                logger.info(f"[线程 {self.thread_id}] 第 {page_num} 页获取到 {len(page_result['results'])} 个结果")
            else:
                logger.warning(f"[线程 {self.thread_id}] 第 {page_num} 页没有获取到结果")

            # 检查是否有下一页
            has_next = page_result['has_next']

            # 如果错误计数超过阈值，更换代理
            if error_count >= max_errors_before_proxy_change and self.proxy_manager:
                logger.warning(f"[线程 {self.thread_id}] 连续遇到 {error_count} 次错误，更换代理")

                proxy = self.proxy_manager.get_best_proxy()
                if proxy:
                    logger.info(f"[线程 {self.thread_id}] 更换为新代理: {proxy}")
                else:
                    logger.warning(f"[线程 {self.thread_id}] 没有可用的新代理")

                error_count = 0

            # 页面间延迟
            if has_next and (max_pages is None or page_num < max_pages):
                delay = random.uniform(*CrawlerConfig.DELAYS['page'])
                time.sleep(delay)

            page_num += 1

        logger.info(f"[线程 {self.thread_id}] 搜索完成，共获取 {len(all_results)} 个结果")
        return all_results
    
    def get_pool_status(self):
        """获取Cookie池状态"""
        if self.use_cookie_pool and self.cookie_pool:
            return self.cookie_pool.get_pool_status()
        else:
            return {"message": "未使用Cookie池模式"}
    
    def refresh_cookie_pool(self):
        """刷新Cookie池"""
        if self.use_cookie_pool and self.cookie_pool:
            self.cookie_pool.refresh_all_cookies()
            logger.info(f"[线程 {self.thread_id}] Cookie池已刷新")
        else:
            logger.warning(f"[线程 {self.thread_id}] 未使用Cookie池模式，无法刷新")
    
    def close(self):
        """关闭爬虫，释放资源"""
        try:
            if self.session:
                self.session.close()
            
            if not self.use_cookie_pool and hasattr(self, 'cookie_manager'):
                # 单Cookie模式下关闭cookie管理器
                pass
            
            logger.info(f"[线程 {self.thread_id}] 爬虫资源已释放")
            
        except Exception as e:
            logger.error(f"[线程 {self.thread_id}] 关闭爬虫时出错: {e}")
