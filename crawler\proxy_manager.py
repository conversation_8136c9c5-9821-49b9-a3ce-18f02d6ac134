# -*- coding: utf-8 -*-
"""
Proxy Manager - 代理管理器
"""
import time
import random
import logging
from typing import Optional, List
import requests
from config import ProxyConfig

logger = logging.getLogger('ProxyManager')


class ProxyManager:
    """代理管理器"""
    
    def __init__(self, api_name='rola', num=5, minute=5):
        """
        初始化代理管理器
        
        Args:
            api_name: 代理API名称 ('rola' 或 'qg')
            num: 获取代理数量
            minute: 代理有效时间（分钟）
        """
        self.api_name = api_name
        self.num = num
        self.minute = minute
        
        # 获取API配置
        if api_name in ProxyConfig.PROXY_APIS:
            api_config = ProxyConfig.PROXY_APIS[api_name]
            if api_name == 'rola':
                self.proxy_url = api_config['url'].format(num=num, minute=minute)
            else:  # qg
                self.proxy_url = api_config['url'].format(num=num)
        else:
            raise ValueError(f"不支持的代理API: {api_name}")
        
        self.proxy_list: List[str] = []
        self.last_update = 0
        self.update_interval = ProxyConfig.UPDATE_INTERVAL
        
        logger.info(f"代理管理器初始化完成，使用API: {api_name}")
    
    def update_proxy_list(self):
        """从API获取新代理并更新列表"""
        try:
            logger.info(f"正在从 {self.api_name} API 获取代理...")
            response = requests.get(self.proxy_url, timeout=10)
            
            if response.status_code == 200:
                text = response.text.strip()
                
                # 检查响应内容有效性
                if '未加入白名单' in text or '认证失败' in text or '余额不足' in text:
                    logger.error(f"代理API返回错误: {text[:100]}")
                    return False
                
                # 解析代理列表
                proxies = [line.strip() for line in text.split('\n') if line.strip()]
                
                if proxies:
                    self.proxy_list = proxies
                    self.last_update = time.time()
                    logger.info(f"成功获取 {len(proxies)} 个代理")
                    return True
                else:
                    logger.warning("代理API返回空列表")
                    return False
            else:
                logger.error(f"代理API请求失败，状态码: {response.status_code}")
                return False
                
        except Exception as e:
            logger.error(f"获取代理失败: {e}")
            return False
    
    def get_random_proxy(self) -> Optional[str]:
        """获取随机代理"""
        # 检查代理列表是否需要更新
        current_time = time.time()
        need_update = (
            not self.proxy_list or 
            (current_time - self.last_update) > self.update_interval
        )
        
        if need_update:
            if not self.update_proxy_list():
                logger.warning("代理列表更新失败")
        
        if not self.proxy_list:
            logger.warning("代理列表为空，返回None")
            return None
        
        try:
            # 随机选择一个代理
            proxy = random.choice(self.proxy_list)
            
            # 移除相同IP的代理（避免重复使用）
            ip = proxy.split(':')[0]
            self.proxy_list = [p for p in self.proxy_list if not p.startswith(ip)]
            
            logger.debug(f"获取代理: {proxy}")
            return proxy
            
        except Exception as e:
            logger.error(f"获取代理时出错: {e}")
            return None
    
    def get_proxy_count(self) -> int:
        """获取当前代理池大小"""
        return len(self.proxy_list)
    
    def is_proxy_available(self) -> bool:
        """检查是否有可用代理"""
        return len(self.proxy_list) > 0


class SmartProxyManager(ProxyManager):
    """智能代理管理器 - 带有代理质量评估"""
    
    def __init__(self, api_name='rola', num=5, minute=5):
        super().__init__(api_name, num, minute)
        self.proxy_stats = {}  # {proxy: {'success': 0, 'total': 0, 'avg_time': 0}}
        
    def update_proxy_stats(self, proxy: str, success: bool, response_time: float):
        """更新代理统计信息"""
        if proxy not in self.proxy_stats:
            self.proxy_stats[proxy] = {
                'success': 0, 
                'total': 0, 
                'times': []
            }
        
        stats = self.proxy_stats[proxy]
        stats['total'] += 1
        if success:
            stats['success'] += 1
        stats['times'].append(response_time)
        
        # 保持最近50次记录
        if len(stats['times']) > 50:
            stats['times'] = stats['times'][-50:]
    
    def get_best_proxy(self) -> Optional[str]:
        """获取最佳代理（基于成功率和响应时间）"""
        if not self.proxy_list:
            return self.get_random_proxy()
        
        best_proxy = None
        best_score = 0
        
        for proxy in self.proxy_list:
            if proxy in self.proxy_stats:
                stats = self.proxy_stats[proxy]
                if stats['total'] >= 5:  # 至少有5次记录
                    success_rate = stats['success'] / stats['total']
                    avg_time = sum(stats['times']) / len(stats['times'])
                    
                    # 综合评分：成功率权重0.7，速度权重0.3
                    score = success_rate * 0.7 + (1 / max(avg_time, 0.1)) * 0.3
                    
                    if score > best_score:
                        best_score = score
                        best_proxy = proxy
        
        # 如果没有找到最佳代理，返回随机代理
        if best_proxy is None:
            return self.get_random_proxy()
        
        # 从列表中移除选中的代理
        if best_proxy in self.proxy_list:
            self.proxy_list.remove(best_proxy)
        
        return best_proxy
    
    def get_proxy_stats_summary(self) -> dict:
        """获取代理统计摘要"""
        if not self.proxy_stats:
            return {}
        
        total_requests = sum(stats['total'] for stats in self.proxy_stats.values())
        total_success = sum(stats['success'] for stats in self.proxy_stats.values())
        
        return {
            'total_proxies': len(self.proxy_stats),
            'total_requests': total_requests,
            'overall_success_rate': total_success / total_requests if total_requests > 0 else 0,
            'available_proxies': len(self.proxy_list)
        }
