# -*- coding: utf-8 -*-
"""
Google Search Email Crawler - Main Entry Point
Author: ji<PERSON> wei
Create Time: 2025/3/19 20:30
File Name: crawler/main.py
"""
import sys
import os

# 添加上级目录到Python路径，以便导入db模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def main():
    """
    主函数 - 配置参数并启动爬虫
    """
    # ==================== 配置参数 ====================
    # 基本配置
    threads = 4          # 线程数量
    batch = 4           # 批处理大小
    headless = True
    use_proxy = False
    from spider_manager import GoogleCrawlerManager
    
    manager = GoogleCrawlerManager(
        threads=threads,
        batch_size=batch,
        headless=headless,
        use_proxy=use_proxy
    )
    
    # 启动爬虫
    manager.run()


if __name__ == "__main__":
    main()
