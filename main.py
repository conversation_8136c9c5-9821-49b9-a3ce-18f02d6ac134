# -*- coding: utf-8 -*-
"""
Google Search Email Crawler - Main Entry Point
Author: jian wei
Create Time: 2025/3/19 20:30
File Name: main.py
"""

def main():
    """
    主函数 - 配置参数并启动爬虫
    """
    # ==================== 配置参数 ====================
    # 基本配置
    threads = 4          # 线程数量
    batch = 4           # 批处理大小

    # Cookie获取配置
    headless = True     # 获取Cookie时是否无头模式 (True=无头, False=有界面)

    # 代理配置
    use_proxy = False   # 是否使用代理 (True=使用, False=不使用)

    # ==================== 启动爬虫 ====================
    from crawler.spider_manager import GoogleCrawlerManager

    manager = GoogleCrawlerManager(
        threads=threads,
        batch_size=batch,
        headless=headless,
        use_proxy=use_proxy
    )

    # 启动爬虫
    manager.run()


if __name__ == "__main__":
    main()
