# -*- coding: utf-8 -*-
"""
Cookie池功能测试脚本
测试Cookie池的创建、管理、轮换和重试机制
"""

import time
import logging
from config import setup_logging
from cookie_pool import CookiePool
from pool_based_spider import PoolBasedGoogleSpider
from pool_spider_manager import PoolSpiderManager

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


def test_cookie_pool_basic():
    """测试Cookie池基本功能"""
    print("\n" + "="*50)
    print("测试Cookie池基本功能")
    print("="*50)
    
    # 创建Cookie池
    pool = CookiePool(pool_size=3, max_failures=2)
    
    # 显示初始状态
    status = pool.get_pool_status()
    print(f"初始状态: {status['active_cookies']}/{status['total_cookies']} 活跃Cookie")
    
    # 获取可用Cookie
    cookie = pool.get_available_cookie()
    if cookie:
        print(f"获取到Cookie: {cookie.id[:8]}...")
        
        # 模拟使用成功
        pool.mark_cookie_success(cookie.id)
        print(f"标记Cookie成功")
        
        # 模拟使用失败
        for i in range(3):
            pool.mark_cookie_failed(cookie.id)
            print(f"标记Cookie失败 {i+1} 次")
    
    # 显示最终状态
    final_status = pool.get_pool_status()
    print(f"最终状态: {final_status['active_cookies']}/{final_status['total_cookies']} 活跃Cookie")
    
    # 显示详细信息
    print("\nCookie详情:")
    for cookie_detail in final_status['cookies_detail']:
        print(f"  {cookie_detail['id']}: {cookie_detail['status']} (失败{cookie_detail['failure_count']}次)")


def test_pool_based_spider():
    """测试基于Cookie池的爬虫"""
    print("\n" + "="*50)
    print("测试基于Cookie池的爬虫")
    print("="*50)
    
    # 创建爬虫实例
    spider = PoolBasedGoogleSpider(
        thread_id=0,
        headless=True,
        use_proxy=False,
        use_cookie_pool=True
    )
    
    try:
        # 显示Cookie池状态
        pool_status = spider.get_pool_status()
        print(f"Cookie池状态: {pool_status['active_cookies']}/{pool_status['total_cookies']} 活跃")
        
        # 执行搜索测试
        test_keywords = ["python programming", "machine learning"]
        
        for keyword in test_keywords:
            print(f"\n测试搜索: {keyword}")
            
            # 搜索第一页
            result = spider.search_page(keyword, page=1)
            
            if result['results']:
                print(f"搜索成功: 获取到 {len(result['results'])} 个结果")
                print(f"使用的Cookie: {result.get('cookie_id', 'Unknown')[:8]}...")
                
                # 显示前3个结果
                for i, res in enumerate(result['results'][:3]):
                    print(f"  {i+1}. {res.get('title', 'No title')}")
            else:
                print(f"搜索失败或无结果")
            
            # 添加延迟
            time.sleep(2)
        
        # 显示最终Cookie池状态
        final_status = spider.get_pool_status()
        print(f"\n最终Cookie池状态:")
        for cookie_detail in final_status['cookies_detail']:
            print(f"  {cookie_detail['id']}: {cookie_detail['status']} (失败{cookie_detail['failure_count']}次)")
    
    finally:
        spider.close()


def test_cookie_failure_simulation():
    """测试Cookie失败和重试机制"""
    print("\n" + "="*50)
    print("测试Cookie失败和重试机制")
    print("="*50)
    
    # 创建小型Cookie池用于测试
    pool = CookiePool(pool_size=2, max_failures=1)
    
    print(f"创建测试Cookie池，大小: 2, 失败阈值: 1")
    
    # 获取所有Cookie并模拟失败
    active_cookies = pool.get_active_cookies()
    print(f"当前活跃Cookie数量: {len(active_cookies)}")
    
    for i, cookie in enumerate(active_cookies):
        print(f"\n模拟Cookie {i+1} ({cookie.id[:8]}...) 失败:")
        
        # 标记失败直到超过阈值
        for fail_count in range(2):  # 失败2次，超过阈值1
            pool.mark_cookie_failed(cookie.id)
            print(f"  失败次数: {cookie.failure_count}, 状态: {cookie.status}")
    
    # 检查池状态
    status = pool.get_pool_status()
    print(f"\n失败模拟后状态: {status['active_cookies']}/{status['total_cookies']} 活跃Cookie")
    
    # 清理失效Cookie
    pool.cleanup_failed_cookies()
    final_status = pool.get_pool_status()
    print(f"清理后状态: {final_status['active_cookies']}/{final_status['total_cookies']} 活跃Cookie")


def test_spider_manager():
    """测试爬虫管理器"""
    print("\n" + "="*50)
    print("测试Cookie池爬虫管理器")
    print("="*50)
    
    # 创建管理器
    manager = PoolSpiderManager(
        num_threads=2,
        batch_size=10,
        use_proxy=False,
        use_cookie_pool=True
    )
    
    # 显示初始状态
    status = manager.get_pool_status()
    print("初始状态:")
    print(f"  线程数: {status['num_threads']}")
    print(f"  批处理大小: {status['batch_size']}")
    print(f"  使用Cookie池: {status['use_cookie_pool']}")
    
    if status['use_cookie_pool']:
        pool_status = status['cookie_pool']
        print(f"  Cookie池: {pool_status['active_cookies']}/{pool_status['total_cookies']} 活跃")
    
    # 测试Cookie池操作
    print("\n测试Cookie池操作:")
    
    # 刷新Cookie池
    print("刷新Cookie池...")
    manager.refresh_cookie_pool()
    
    # 清理失效Cookie
    print("清理失效Cookie...")
    manager.cleanup_failed_cookies()
    
    # 监控池健康状态
    print("监控池健康状态...")
    manager.monitor_pool_health()
    
    # 显示最终状态
    final_status = manager.get_pool_status()
    manager.print_status_report()


def run_comprehensive_test():
    """运行综合测试"""
    print("开始Cookie池功能综合测试")
    print("测试时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 基本功能测试
        test_cookie_pool_basic()
        
        # 爬虫功能测试
        test_pool_based_spider()
        
        # 失败机制测试
        test_cookie_failure_simulation()
        
        # 管理器测试
        test_spider_manager()
        
        print("\n" + "="*50)
        print("所有测试完成!")
        print("="*50)
        
    except Exception as e:
        logger.error(f"测试过程中出现异常: {e}")
        import traceback
        traceback.print_exc()


def interactive_test():
    """交互式测试"""
    print("\n" + "="*50)
    print("Cookie池交互式测试")
    print("="*50)
    
    # 创建Cookie池
    pool = CookiePool(pool_size=3)
    spider = PoolBasedGoogleSpider(use_cookie_pool=True)
    spider.cookie_pool = pool
    
    try:
        while True:
            print("\n可用操作:")
            print("1. 显示Cookie池状态")
            print("2. 执行搜索测试")
            print("3. 模拟Cookie失败")
            print("4. 刷新Cookie池")
            print("5. 清理失效Cookie")
            print("0. 退出")
            
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                status = pool.get_pool_status()
                print(f"\nCookie池状态: {status['active_cookies']}/{status['total_cookies']} 活跃")
                for cookie_detail in status['cookies_detail']:
                    print(f"  {cookie_detail['id']}: {cookie_detail['status']} (失败{cookie_detail['failure_count']}次)")
            
            elif choice == '2':
                keyword = input("请输入搜索关键词: ").strip()
                if keyword:
                    print(f"搜索: {keyword}")
                    result = spider.search_page(keyword, page=1)
                    if result['results']:
                        print(f"成功获取 {len(result['results'])} 个结果")
                    else:
                        print("搜索失败或无结果")
            
            elif choice == '3':
                cookie = pool.get_available_cookie()
                if cookie:
                    pool.mark_cookie_failed(cookie.id)
                    print(f"标记Cookie {cookie.id[:8]}... 失败")
                else:
                    print("没有可用的Cookie")
            
            elif choice == '4':
                print("刷新Cookie池...")
                pool.refresh_all_cookies()
                print("刷新完成")
            
            elif choice == '5':
                print("清理失效Cookie...")
                pool.cleanup_failed_cookies()
                print("清理完成")
            
            else:
                print("无效选择，请重试")
    
    finally:
        spider.close()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1 and sys.argv[1] == "interactive":
        interactive_test()
    else:
        run_comprehensive_test()
