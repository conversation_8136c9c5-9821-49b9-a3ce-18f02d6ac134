# -*- coding: utf-8 -*-
"""
Google Spider - 谷歌搜索爬虫核心模块
"""
import random
import time
import logging
import re
import threading
import requests
from lxml import etree
from config import CrawlerConfig
from cookie_manager import GoogleCookieManager
from proxy_manager import SmartProxyManager

logger = logging.getLogger('GoogleSpider')


class EmailExtractor:
    """邮箱提取器"""
    
    @staticmethod
    def extract_email(content):
        """从内容中提取邮箱地址"""
        if not content:
            return None
        
        # 基本邮箱正则表达式 - 更严格的域名匹配
        basic_pattern = r'[\w\.-]+@[\w\.-]+\.(?:com|net|org|edu|gov|mil|io|co|uk|cn|de|fr|jp|au|ru|ca|it|nl|es|br|in|mx|ch|se|no|dk|fi|pl|cz|at|be|ie|nz|sg|hu|pt|gr|il|za|tr|ro|hk|kr|tw|vn|id|th|my|ph|sa|ae|qa|pk|bd|ng|ke|tz|gh|et|dz|ma|tn|eg|ly|zw|zm|mw|mu|mg|ci|cm|sn|cd|ao|na|bw|ls|sz|rw|bi|tg|bj|ne|ml|mr|td|gm|sl|lr|gn|gw|cv|st|ga|cg|cf|sd|ss|er|dj|so)'
        
        # 尝试提取标准邮箱
        emails = re.findall(basic_pattern, content, re.IGNORECASE)
        if emails:
            return emails[0].strip()
        
        # 处理带空格的邮箱
        spaced_email_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*(com|net|org|edu|gov|mil|io|co|uk|cn|de|fr|jp|au|ru|ca|it|nl|es|br|in|mx)'
        spaced_match = re.search(spaced_email_pattern, content, re.IGNORECASE)
        if spaced_match:
            return f"{spaced_match.group(1)}@{spaced_match.group(2)}.{spaced_match.group(3)}"
        
        # 针对特殊情况：邮箱后面紧跟着句点和文本
        special_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*(com|net|org|edu|gov|mil|io|co|uk)(?:\s*\.\s*|\s+)'
        special_match = re.search(special_pattern, content, re.IGNORECASE)
        if special_match:
            return f"{special_match.group(1)}@{special_match.group(2)}.{special_match.group(3)}"
        
        # 尝试查找并组合分散的邮箱部分
        if '@' in content:
            combined_pattern = r'([\w\.-]+)\s*@\s*([\w\.-]+)\s*\.\s*([a-zA-Z]{2,6})'
            combined_match = re.search(combined_pattern, content)
            if combined_match:
                username = combined_match.group(1).strip()
                domain = combined_match.group(2).strip()
                tld = combined_match.group(3).strip()
                return f"{username}@{domain}.{tld}"
        
        return None
    
    @staticmethod
    def validate_email(email):
        """验证邮箱格式是否正确"""
        if not email:
            return False
        
        # 基本格式验证
        pattern = r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
        return bool(re.match(pattern, email))


class GoogleSearcher:
    """Google搜索器"""
    
    def __init__(self, thread_id=0, headless=True, use_proxy=False):
        """
        初始化搜索器
        
        Args:
            thread_id: 线程ID
            headless: 是否无头模式
            use_proxy: 是否使用代理
        """
        self.thread_id = thread_id
        self.use_proxy = use_proxy
        
        # 初始化Cookie管理器
        cookie_file = f"google_cookies_{thread_id}.json"
        self.cookie_manager = GoogleCookieManager(
            cookie_file=cookie_file,
            headless=headless,
            use_proxy=use_proxy
        )
        
        # 初始化代理管理器
        if use_proxy:
            self.proxy_manager = SmartProxyManager(num=10, minute=5)
        else:
            self.proxy_manager = None
        
        # 初始化HTTP会话
        self.session = requests.Session()
        adapter = requests.adapters.HTTPAdapter(
            pool_connections=10,
            pool_maxsize=20,
            max_retries=3
        )
        self.session.mount('http://', adapter)
        self.session.mount('https://', adapter)
        
        # 线程锁
        self.lock = threading.Lock()
        
        # 邮箱提取器
        self.email_extractor = EmailExtractor()
        
        logger.info(f"[线程 {self.thread_id}] 搜索器初始化完成")
    
    def get_headers(self):
        """生成带有随机User-Agent的请求头"""
        headers = CrawlerConfig.BASE_HEADERS.copy()
        headers['user-agent'] = random.choice(CrawlerConfig.USER_AGENTS)
        return headers
    
    def check_cookies_valid(self, page):
        """检查页面是否有有效的搜索结果"""
        try:
            h3_elements = page.xpath('//h3')
            return len(h3_elements) >= 1
        except:
            return False

    def _is_blocked_page(self, html_content):
        """检查是否是被封禁的页面"""
        if not html_content:
            return False

        # 检查常见的封禁关键词
        blocked_indicators = [
            "unusual traffic",
            "automated queries",
            "robot",
            "captcha",
            "verify you're not a robot",
            "suspicious activity",
            "blocked",
            "access denied"
        ]

        html_lower = html_content.lower()
        for indicator in blocked_indicators:
            if indicator in html_lower:
                return True

        return False
    
    def search_page(self, keyword, page_num=0, max_retries=3, proxy=None, error_count=0):
        """
        搜索特定页码的结果并提取URL和邮箱
        
        Args:
            keyword: 搜索关键词
            page_num: 页码 (0表示第一页)
            max_retries: 最大重试次数
            proxy: 使用的代理
            error_count: 当前错误计数
            
        Returns:
            结果字典，包含results和has_next，以及更新后的proxy和error_count
        """
        # 搜索参数
        params = {
            'q': keyword,
            'oq': keyword,
            'start': page_num * 10,  # Google每页10个结果
            'gs_lcrp': '',
            'sourceid': 'chrome',
            'ie': 'UTF-8',
        }
        
        # 设置代理
        proxy_server = None
        if proxy:
            proxy_server = {
                "http": f"http://{proxy}",
                "https": f"http://{proxy}"
            }
        
        for attempt in range(max_retries):
            try:
                # 获取cookies
                cookies = self.cookie_manager.get_valid_cookies()
                
                # 发送请求
                headers = self.get_headers()
                start_time = time.time()
                
                response = self.session.get(
                    url='https://www.google.com/search',
                    headers=headers,
                    cookies=cookies,
                    params=params,
                    timeout=CrawlerConfig.REQUEST_TIMEOUT,
                    proxies=proxy_server
                )
                
                response_time = time.time() - start_time
                
                if response.status_code == 200:
                    # 更新代理统计
                    if self.proxy_manager and proxy:
                        self.proxy_manager.update_proxy_stats(proxy, True, response_time)

                    # 调试信息：检查响应内容
                    logger.debug(f"[线程 {self.thread_id}] 响应长度: {len(response.text)} 字符")

                    # 检查是否包含搜索结果的关键标识
                    if 'id="search"' not in response.text and 'class="g"' not in response.text:
                        logger.warning(f"[线程 {self.thread_id}] 响应中没有找到搜索结果容器")
                        logger.debug(f"[线程 {self.thread_id}] 响应前500字符: {response.text[:500]}")

                    page = etree.HTML(response.text)
                    
                    # 检查是否有有效结果 - 只有在明确被封禁时才更新cookie
                    if not self.check_cookies_valid(page):
                        # 检查是否是真正的封禁页面
                        if self._is_blocked_page(response.text):
                            logger.warning(f"[线程 {self.thread_id}] 检测到封禁页面，强制刷新cookie")
                            with self.lock:
                                self.cookie_manager.force_refresh_cookies()
                            error_count += 1
                            continue
                        else:
                            logger.debug(f"[线程 {self.thread_id}] 页面结果较少，但cookie仍然有效")
                            # 即使结果少也继续使用当前cookie
                    
                    # 提取搜索结果
                    results = self._extract_search_results(page)
                    
                    # 检查是否有下一页
                    has_next = len(page.xpath('//a[@id="pnnext"]')) > 0
                    
                    # 成功获取结果，重置错误计数
                    return {
                        'results': results,
                        'has_next': has_next,
                        'proxy': proxy,
                        'error_count': 0  # 成功后重置错误计数
                    }
                
                elif response.status_code == 429:
                    logger.warning(f"[线程 {self.thread_id}] 请求被限制，状态码: {response.status_code}")
                    # 只有在429错误时才更新cookie（真正的限制）
                    if self._is_blocked_page(response.text):
                        logger.warning(f"[线程 {self.thread_id}] 确认被封禁，强制刷新cookie")
                        with self.lock:
                            self.cookie_manager.force_refresh_cookies()
                    error_count += 1
                
                else:
                    logger.warning(f"[线程 {self.thread_id}] 请求返回状态码: {response.status_code}")
                    error_count += 1
                
                # 更新代理统计（失败）
                if self.proxy_manager and proxy:
                    self.proxy_manager.update_proxy_stats(proxy, False, response_time)
                
            except Exception as e:
                logger.error(f'[线程 {self.thread_id}] 请求错误: {e}, 尝试第 {attempt + 1}/{max_retries} 次')
                error_count += 1
                
                # 更新代理统计（失败）
                if self.proxy_manager and proxy:
                    self.proxy_manager.update_proxy_stats(proxy, False, 10.0)
            
            # 等待一段时间后重试
            if attempt < max_retries - 1:
                delay = random.uniform(*CrawlerConfig.DELAYS['retry'])
                time.sleep(delay)
        
        logger.error(f"[线程 {self.thread_id}] 所有 {max_retries} 次尝试均失败")
        
        # 返回失败结果和更新的错误计数
        return {
            'results': [],
            'has_next': False,
            'proxy': proxy,
            'error_count': error_count
        }

    def _extract_search_results(self, page):
        """从页面中提取搜索结果"""
        results = []

        try:
            # 提取搜索结果URL - 尝试多种选择器
            url_elements = page.xpath('//a[@jsname="UWckNb"]/@href')

            # 如果第一种选择器没有结果，尝试其他选择器
            if not url_elements:
                logger.debug(f"[线程 {self.thread_id}] 第一种URL选择器无结果，尝试其他选择器...")
                url_elements = page.xpath('//h3/a/@href')  # 备用选择器1

            if not url_elements:
                url_elements = page.xpath('//div[@class="g"]//a/@href')  # 备用选择器2

            if not url_elements:
                url_elements = page.xpath('//a[contains(@href, "http")]/@href')  # 通用选择器

            logger.debug(f"[线程 {self.thread_id}] 找到 {len(url_elements)} 个URL链接")

            # 获取URL和内容列表
            urls = [url for url in url_elements if url.startswith('http')]  # 过滤有效URL
            logger.debug(f"[线程 {self.thread_id}] 过滤后有效URL: {len(urls)} 个")
            contents = []

            # 提取每个结果的内容
            for i in range(len(url_elements)):
                try:
                    content_xpath = f'(//div[@jscontroller="SC7lYd"]//*[@data-snf="nke7rc"])[{i + 1}]//text()'
                    content_parts = page.xpath(content_xpath)
                    content = ' '.join(content_parts) if content_parts else ''
                    contents.append(content)
                except:
                    contents.append('')

            # 确保内容列表长度与URL列表相同
            while len(contents) < len(urls):
                contents.append('')

            # 提取结果
            for i, url in enumerate(urls):
                try:
                    content = contents[i] if i < len(contents) else ''

                    # 提取邮箱
                    email = self.email_extractor.extract_email(content)
                    if email and self.email_extractor.validate_email(email):
                        results.append((url, email))

                except Exception as e:
                    logger.debug(f"[线程 {self.thread_id}] 提取结果 {i + 1} 时出错: {e}")

        except Exception as e:
            logger.error(f"[线程 {self.thread_id}] 提取搜索结果时出错: {e}")

        return results

    def search(self, keyword, max_pages=None, max_errors_before_proxy_change=3):
        """
        搜索并获取所有页面的结果

        Args:
            keyword: 搜索关键词
            max_pages: 最大爬取页数，None表示爬取所有页面
            max_errors_before_proxy_change: 更换代理前允许的最大错误次数

        Returns:
            所有页面的结果列表，每个元素是(page_num, url, email)元组
        """
        all_results = []
        page_num = 0
        has_next = True
        error_count = 0

        # 初始获取一个代理
        proxy = None
        if self.proxy_manager:
            proxy = self.proxy_manager.get_best_proxy()
            if proxy:
                logger.info(f"[线程 {self.thread_id}] 使用代理: {proxy}")
            else:
                logger.warning(f"[线程 {self.thread_id}] 没有可用代理")

        while has_next and (max_pages is None or page_num < max_pages):
            # 使用当前代理搜索页面
            page_result = self.search_page(keyword, page_num, CrawlerConfig.MAX_RETRIES, proxy, error_count)

            # 更新代理和错误计数
            proxy = page_result['proxy']
            error_count = page_result['error_count']

            # 如果错误计数超过阈值，更换代理
            if error_count >= max_errors_before_proxy_change and self.proxy_manager:
                logger.warning(f"[线程 {self.thread_id}] 连续遇到 {error_count} 次错误，更换代理")

                # 获取新代理
                proxy = self.proxy_manager.get_best_proxy()
                if proxy:
                    logger.info(f"[线程 {self.thread_id}] 更换为新代理: {proxy}")
                else:
                    logger.warning(f"[线程 {self.thread_id}] 没有可用的新代理")

                error_count = 0  # 重置错误计数

            if not page_result['results']:
                logger.warning(f"[线程 {self.thread_id}] 第 {page_num + 1} 页没有找到结果")
                logger.warning(f"[线程 {self.thread_id}] 错误计数: {error_count}, has_next: {page_result['has_next']}")

                # 如果是第一页就没有结果，可能是搜索词问题或被封禁
                if page_num == 0:
                    logger.error(f"[线程 {self.thread_id}] 第一页就没有结果，可能的原因:")
                    logger.error(f"  1. 搜索词 '{keyword}' 确实没有相关结果")
                    logger.error(f"  2. Cookie已失效，需要更新")
                    logger.error(f"  3. 被Google检测并限制访问")
                    logger.error(f"  4. 网络连接问题")

                # 如果有下一页标识，继续尝试下一页
                if page_result['has_next'] and page_num < 3:  # 最多尝试3页
                    logger.info(f"[线程 {self.thread_id}] 尝试继续搜索下一页...")
                    page_num += 1
                    continue
                else:
                    logger.warning(f"[线程 {self.thread_id}] 停止搜索")
                    break

            # 添加当前页面的结果
            for url, email in page_result['results']:
                all_results.append((page_num + 1, url, email))

            has_next = page_result['has_next']
            logger.debug(f"[线程 {self.thread_id}] 已爬取第 {page_num + 1} 页，找到 {len(page_result['results'])} 个结果")

            page_num += 1

            # 页面间添加随机延迟
            if has_next and (max_pages is None or page_num < max_pages):
                delay = random.uniform(*CrawlerConfig.DELAYS['page'])
                time.sleep(delay)

        logger.info(f"[线程 {self.thread_id}] 完成搜索 '{keyword}'，共爬取 {page_num} 页，找到 {len(all_results)} 个结果")

        return all_results

    def close(self):
        """关闭搜索器，释放资源"""
        try:
            self.session.close()
        except:
            pass
