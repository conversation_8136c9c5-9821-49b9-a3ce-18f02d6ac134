# -*- coding: utf-8 -*-
"""
<PERSON><PERSON> Manager - <PERSON><PERSON>管理器
"""
import asyncio
import random
import logging
import json
import os
from playwright.async_api import async_playwright
from config import CookieConfig
from proxy_manager import ProxyManager

logger = logging.getLogger('CookieManager')


class HumanBehaviorSimulator:
    """人类行为模拟工具类"""
    
    @staticmethod
    def get_random_viewport():
        """生成随机设备视口"""
        return {
            "width": random.choice([1920, 1366, 1536, 1440]),
            "height": random.choice([1080, 768, 864, 900])
        }
    
    @staticmethod
    async def simulate_typing(element, text):
        """模拟人类输入行为"""
        for char in text:
            await element.type(char)
            await asyncio.sleep(random.uniform(0.08, 0.15))
            if random.random() < 0.07:
                await element.press("Backspace")
                await element.type(char)
    
    @staticmethod
    async def random_mouse_movement(page):
        """生成随机鼠标轨迹"""
        for _ in range(random.randint(3, 5)):
            x = random.randint(0, page.viewport_size['width'])
            y = random.randint(0, page.viewport_size['height'])
            await page.mouse.move(x, y, steps=random.randint(2, 5))
            await asyncio.sleep(random.uniform(0.3, 0.7))
    
    @staticmethod
    async def simulate_scroll(page):
        """模拟自然滚动"""
        scroll_distance = random.randint(300, 800)
        await page.mouse.wheel(0, scroll_distance)
        await asyncio.sleep(random.uniform(0.5, 1.2))
    
    @staticmethod
    async def random_page_interaction(page):
        """随机页面交互"""
        actions = [
            lambda: page.mouse.wheel(0, random.randint(200, 500)),
            lambda: HumanBehaviorSimulator.random_mouse_movement(page),
            lambda: page.keyboard.press('PageDown'),
            lambda: asyncio.sleep(random.uniform(0.5, 1.5))
        ]
        for _ in range(random.randint(2, 4)):
            await random.choice(actions)()
            await asyncio.sleep(random.uniform(0.3, 0.8))


class GoogleCookieManager:
    """Google Cookie管理器"""
    
    def __init__(self, cookie_file="google_cookies.json", headless=True, use_proxy=False):
        """
        初始化Cookie管理器
        
        Args:
            cookie_file: Cookie文件路径
            headless: 是否无头模式
            use_proxy: 是否使用代理
        """
        self.cookies = {}
        self.cookie_file = cookie_file
        self.headless = headless
        self.use_proxy = use_proxy
        self.human_simulator = HumanBehaviorSimulator()
        
        # 确保cookie目录存在
        if not os.path.exists(CookieConfig.COOKIE_DIR):
            os.makedirs(CookieConfig.COOKIE_DIR)
        
        # 如果cookie_file不包含路径，则放在cookies目录下
        if '/' not in cookie_file and '\\' not in cookie_file:
            self.cookie_file = os.path.join(CookieConfig.COOKIE_DIR, cookie_file)
        
        self.load_cookies_from_file()
        logger.info(f"Cookie管理器初始化完成，文件: {self.cookie_file}")
    
    def load_cookies_from_file(self):
        """从文件加载cookies"""
        if os.path.exists(self.cookie_file):
            try:
                with open(self.cookie_file, 'r', encoding='utf-8') as f:
                    self.cookies = json.load(f)
                
                if not self.cookies:
                    logger.info("Cookie文件为空，将获取新的cookies")
                    self.create_cookies()
                else:
                    logger.info("成功从文件加载cookies")
                    
            except Exception as e:
                logger.warning(f"加载cookies文件失败: {e}")
                self.cookies = {}
        else:
            logger.info("Cookie文件不存在，将创建新的cookies")
            self.create_cookies()
    
    def save_cookies_to_file(self):
        """保存cookies到文件"""
        try:
            with open(self.cookie_file, 'w', encoding='utf-8') as f:
                json.dump(self.cookies, f, ensure_ascii=False, indent=2)
            logger.info("Cookies已保存到文件")
        except Exception as e:
            logger.error(f"保存cookies失败: {e}")
    
    def get_cookies(self):
        """获取cookies"""
        return self.cookies
    
    def update_cookies(self, new_cookies):
        """更新cookies"""
        self.cookies = new_cookies
        self.save_cookies_to_file()
    
    def is_cookies_valid(self):
        """检查cookies是否有效"""
        if not self.cookies:
            return False
        
        # 检查是否包含必要的cookie字段
        for cookie in CookieConfig.REQUIRED_COOKIES:
            if cookie not in self.cookies:
                logger.debug(f"缺少必要的cookie: {cookie}")
                return False
        
        return True
    
    async def _detect_captcha(self, page):
        """检测是否出现验证码"""
        captcha_selectors = [
            'form#captcha-form',
            'div.g-recaptcha',
            'input[name="captcha"]',
            'title:has-text("unusual traffic")'
        ]
        
        for selector in captcha_selectors:
            try:
                if await page.query_selector(selector):
                    logger.warning("检测到验证码!")
                    return True
            except:
                continue
        return False
    
    async def _perform_search(self, page, query):
        """执行搜索操作"""
        logger.info(f"正在执行搜索操作: {query}")
        
        try:
            await page.goto('https://www.google.com', timeout=CookieConfig.BROWSER_CONFIG['timeout'])
            await page.wait_for_load_state('networkidle')
            
            # 查找搜索框
            search_box = await page.wait_for_selector('textarea[name="q"]', timeout=15000)
            await HumanBehaviorSimulator.simulate_typing(search_box, query)
            await asyncio.sleep(random.uniform(0.8, 1.3))
            await search_box.press('Enter')
            
            # 等待搜索结果
            await page.wait_for_selector('div#search', timeout=20000)
            
            # 检查是否有验证码
            return not await self._detect_captcha(page)
            
        except Exception as e:
            logger.error(f"执行搜索操作失败: {e}")
            return False

    async def fetch_new_cookies(self, search_query=None):
        """获取新的Google cookies"""
        if search_query is None:
            search_query = random.choice(CookieConfig.SEARCH_QUERIES)

        logger.info(f"开始获取新的cookies，搜索词: {search_query}")

        async with async_playwright() as p:
            # 代理配置
            proxy_server = None
            if self.use_proxy:
                try:
                    proxy_manager = ProxyManager(num=2, minute=5)
                    proxy = proxy_manager.get_random_proxy()
                    if proxy and ':' in proxy:
                        proxy_server = {'server': f'http://{proxy}'}
                        logger.info(f"使用代理: {proxy}")
                except Exception as e:
                    logger.warning(f"获取代理失败: {e}")

            # 浏览器配置
            browser_options = {
                'headless': self.headless,
                'args': CookieConfig.BROWSER_CONFIG['args'],
                'ignore_default_args': ['--enable-automation']
            }

            try:
                # 启动浏览器
                browser = await p.chromium.launch(**browser_options, proxy=proxy_server, timeout=60000)

                # 创建上下文
                viewport = self.human_simulator.get_random_viewport()
                from config import CrawlerConfig
                context = await browser.new_context(
                    viewport=viewport,
                    user_agent=random.choice(CrawlerConfig.USER_AGENTS)
                )

                # 禁用WebDriver检测
                await context.add_init_script("""
                    Object.defineProperty(navigator, 'webdriver', {
                        get: () => false,
                    });

                    // 隐藏自动化特征
                    const originalQuery = window.navigator.permissions.query;
                    window.navigator.permissions.query = (parameters) => (
                        parameters.name === 'notifications' ?
                            Promise.resolve({state: Notification.permission}) :
                            originalQuery(parameters)
                    );

                    // 模拟插件
                    Object.defineProperty(navigator, 'plugins', {
                        get: () => [1, 2, 3, 4, 5].map(() => ({
                            0: {
                                type: "application/x-google-chrome-pdf",
                                suffixes: "pdf",
                                description: "Portable Document Format"
                            },
                            name: "Chrome PDF Plugin",
                            filename: "internal-pdf-viewer",
                            description: "Portable Document Format",
                            length: 1
                        }))
                    });
                """)

                # 创建新页面
                page = await context.new_page()

                # 执行搜索操作
                search_success = await self._perform_search(page, search_query)

                if not search_success:
                    logger.warning("搜索操作失败，可能被检测为机器人")
                    return {}

                # 模拟随机互动
                await self.human_simulator.random_page_interaction(page)

                # 获取cookies
                cookies = await context.cookies()
                formatted_cookies = {}

                # 格式化cookies为字典
                for cookie in cookies:
                    if cookie.get('domain', '').endswith('google.com'):
                        formatted_cookies[cookie['name']] = cookie['value']

                if formatted_cookies:
                    # 更新cookie
                    self.update_cookies(formatted_cookies)
                    logger.info("成功获取新的cookies")
                    return formatted_cookies
                else:
                    logger.warning("未获取到有效的cookies")
                    return {}

            except Exception as e:
                logger.error(f"获取cookies时出错: {e}")
                return {}

            finally:
                try:
                    await browser.close()
                except:
                    pass

    def create_cookies(self):
        """创建新的cookies（同步方法）"""
        try:
            cookies = asyncio.run(self.fetch_new_cookies())
            return cookies
        except Exception as e:
            logger.error(f"创建cookies失败: {e}")
            return {}

    def get_valid_cookies(self):
        """获取有效的cookies，优先使用现有cookies"""
        # 只有在cookies完全无效时才重新获取
        if not self.cookies:
            logger.info("没有cookies，正在获取新的cookies...")
            self.create_cookies()
        elif not self.is_cookies_valid():
            logger.warning("cookies格式不完整，但仍尝试使用...")
            # 即使格式不完整也先尝试使用，只有真正被封才重新获取

        return self.get_cookies()

    def force_refresh_cookies(self):
        """强制刷新cookies（只在确认被封时调用）"""
        logger.info("强制刷新cookies...")
        return self.create_cookies()
