# -*- coding: utf-8 -*-
"""
Cookie池使用示例
演示如何使用新的Cookie池功能进行搜索
"""

import time
import logging
from config import setup_logging
from pool_based_spider import PoolBasedGoogleSpider
from pool_spider_manager import create_pool_manager

# 设置日志
setup_logging()
logger = logging.getLogger(__name__)


def example_single_spider():
    """示例1: 使用单个爬虫实例"""
    print("="*60)
    print("示例1: 使用Cookie池的单个爬虫实例")
    print("="*60)
    
    # 创建爬虫实例（启用Cookie池）
    spider = PoolBasedGoogleSpider(
        thread_id=0,
        headless=True,
        use_proxy=False,
        use_cookie_pool=True  # 启用Cookie池
    )
    
    try:
        # 显示Cookie池初始状态
        status = spider.get_pool_status()
        print(f"Cookie池状态: {status['active_cookies']}/{status['total_cookies']} 活跃Cookie")
        
        # 执行多个搜索任务
        keywords = [
            "python web scraping",
            "machine learning tutorial", 
            "data science projects"
        ]
        
        for i, keyword in enumerate(keywords, 1):
            print(f"\n[{i}/{len(keywords)}] 搜索: {keyword}")
            
            # 搜索前3页
            results = spider.search_all_pages(keyword, max_pages=3)
            
            print(f"获取到 {len(results)} 个结果")
            
            # 显示前5个结果
            for j, result in enumerate(results[:5], 1):
                title = result.get('title', 'No title')[:50]
                print(f"  {j}. {title}...")
            
            # 添加延迟
            time.sleep(2)
        
        # 显示最终Cookie池状态
        final_status = spider.get_pool_status()
        print(f"\n最终Cookie池状态:")
        for cookie in final_status['cookies_detail']:
            print(f"  {cookie['id']}: {cookie['status']} (失败{cookie['failure_count']}次)")
    
    finally:
        spider.close()


def example_traditional_mode():
    """示例2: 传统单Cookie模式对比"""
    print("\n" + "="*60)
    print("示例2: 传统单Cookie模式（对比）")
    print("="*60)
    
    # 创建传统模式爬虫
    spider = PoolBasedGoogleSpider(
        thread_id=0,
        headless=True,
        use_proxy=False,
        use_cookie_pool=False  # 禁用Cookie池，使用传统模式
    )
    
    try:
        # 执行搜索
        keyword = "artificial intelligence news"
        print(f"搜索: {keyword}")
        
        results = spider.search_all_pages(keyword, max_pages=2)
        print(f"获取到 {len(results)} 个结果")
        
        # 显示状态（传统模式）
        status = spider.get_pool_status()
        print(f"模式: {status['message']}")
    
    finally:
        spider.close()


def example_manager_usage():
    """示例3: 使用爬虫管理器"""
    print("\n" + "="*60)
    print("示例3: 使用Cookie池爬虫管理器")
    print("="*60)
    
    # 创建管理器
    manager = create_pool_manager(
        num_threads=2,
        batch_size=50,
        use_proxy=False
    )
    
    # 显示管理器状态
    manager.print_status_report()
    
    # 演示管理器功能
    print("演示管理器功能:")
    
    # 监控Cookie池健康状态
    print("1. 监控Cookie池健康状态...")
    manager.monitor_pool_health()
    
    # 清理失效Cookie
    print("2. 清理失效Cookie...")
    manager.cleanup_failed_cookies()
    
    # 显示最终状态
    print("3. 最终状态报告:")
    manager.print_status_report()


def example_failure_recovery():
    """示例4: Cookie失败和恢复机制"""
    print("\n" + "="*60)
    print("示例4: Cookie失败和恢复机制演示")
    print("="*60)
    
    spider = PoolBasedGoogleSpider(use_cookie_pool=True)
    
    try:
        # 获取Cookie池
        pool = spider.cookie_pool
        
        print("初始Cookie池状态:")
        status = pool.get_pool_status()
        print(f"活跃Cookie: {status['active_cookies']}")
        
        # 模拟Cookie失败
        print("\n模拟Cookie失败...")
        active_cookies = pool.get_active_cookies()
        
        if active_cookies:
            # 让第一个Cookie失败多次
            test_cookie = active_cookies[0]
            print(f"测试Cookie: {test_cookie.id[:8]}...")
            
            for i in range(3):  # 失败3次
                pool.mark_cookie_failed(test_cookie.id)
                print(f"  失败 {i+1} 次，状态: {test_cookie.status}")
        
        # 显示失败后状态
        print("\n失败后Cookie池状态:")
        status = pool.get_pool_status()
        print(f"活跃Cookie: {status['active_cookies']}")
        
        # 测试搜索是否仍然工作（应该自动切换到其他Cookie）
        print("\n测试自动Cookie切换...")
        result = spider.search_page("test search", page=1)
        
        if result['results'] or result.get('cookie_id'):
            print("✓ 自动Cookie切换成功")
            if result.get('cookie_id'):
                print(f"使用的Cookie: {result['cookie_id'][:8]}...")
        else:
            print("✗ 自动Cookie切换失败")
        
        # 清理失效Cookie
        print("\n清理失效Cookie...")
        pool.cleanup_failed_cookies()
        
        final_status = pool.get_pool_status()
        print(f"清理后活跃Cookie: {final_status['active_cookies']}")
    
    finally:
        spider.close()


def example_configuration():
    """示例5: 配置选项演示"""
    print("\n" + "="*60)
    print("示例5: 配置选项演示")
    print("="*60)
    
    from config import CookiePoolConfig

    print("当前Cookie池配置:")
    print(f"  池大小: {CookiePoolConfig.POOL_SIZE}")
    print(f"  失败阈值: {CookiePoolConfig.MAX_FAILURES}")
    print(f"  最大重试Cookie数: {CookiePoolConfig.MAX_RETRY_COOKIES}")
    print(f"  最少活跃Cookie: {CookiePoolConfig.MIN_ACTIVE_COOKIES}")
    print(f"  池存储目录: {CookiePoolConfig.POOL_DIR}")

    # 创建自定义配置的Cookie池
    from cookie_pool import CookiePool
    
    print("\n创建自定义配置的Cookie池:")
    custom_pool = CookiePool(
        pool_size=3,        # 小型池
        max_failures=2,     # 失败2次后移除
        pool_dir='custom_pool'
    )
    
    status = custom_pool.get_pool_status()
    print(f"自定义池状态: {status['active_cookies']}/{status['total_cookies']} 活跃Cookie")


def run_all_examples():
    """运行所有示例"""
    print("Cookie池功能使用示例")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    try:
        # 示例1: 单个爬虫
        example_single_spider()
        
        # 示例2: 传统模式对比
        example_traditional_mode()
        
        # 示例3: 管理器使用
        example_manager_usage()
        
        # 示例4: 失败恢复
        example_failure_recovery()
        
        # 示例5: 配置选项
        example_configuration()
        
        print("\n" + "="*60)
        print("所有示例运行完成!")
        print("="*60)
        
    except Exception as e:
        logger.error(f"示例运行过程中出现异常: {e}")
        import traceback
        traceback.print_exc()


def quick_test():
    """快速测试"""
    print("快速测试Cookie池功能...")
    
    spider = PoolBasedGoogleSpider(use_cookie_pool=True)
    
    try:
        # 简单搜索测试
        result = spider.search_page("hello world", page=1)
        
        if result['results']:
            print(f"✓ 搜索成功，获取 {len(result['results'])} 个结果")
        else:
            print("✗ 搜索失败")
        
        # 显示Cookie池状态
        status = spider.get_pool_status()
        print(f"Cookie池: {status['active_cookies']}/{status['total_cookies']} 活跃")
        
    finally:
        spider.close()


if __name__ == "__main__":
    import sys
    
    if len(sys.argv) > 1:
        if sys.argv[1] == "quick":
            quick_test()
        elif sys.argv[1] == "single":
            example_single_spider()
        elif sys.argv[1] == "manager":
            example_manager_usage()
        elif sys.argv[1] == "failure":
            example_failure_recovery()
        elif sys.argv[1] == "config":
            example_configuration()
        else:
            print("可用选项: quick, single, manager, failure, config")
    else:
        run_all_examples()
