# -*- coding: utf-8 -*-
"""
单次搜索测试 - 输入查询语句测试整个流程
"""
import sys
import os
import logging

# 添加项目根目录到Python路径
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

# 设置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('single_search.log'),
        logging.StreamHandler()
    ]
)

def single_search_test(query, headless=True, use_proxy=False, max_pages=2):
    """
    单次搜索测试
    
    Args:
        query: 搜索查询语句
        headless: 是否无头模式
        use_proxy: 是否使用代理
        max_pages: 最大搜索页数
    """
    print("=" * 60)
    print(f"开始搜索测试")
    print(f"查询语句: {query}")
    print(f"无头模式: {headless}")
    print(f"使用代理: {use_proxy}")
    print(f"最大页数: {max_pages}")
    print("=" * 60)
    
    try:
        # 导入爬虫模块
        from crawler.google_spider import GoogleSearcher
        
        # 创建搜索器
        searcher = GoogleSearcher(
            thread_id=0,
            headless=headless,
            use_proxy=use_proxy
        )
        
        print(f"✓ 搜索器初始化完成")
        
        # 执行搜索
        print(f"🔍 开始搜索: {query}")
        results = searcher.search(query, max_pages=max_pages)
        
        # 显示结果
        print(f"\n📊 搜索结果:")
        print(f"总共找到 {len(results)} 个邮箱")
        print("-" * 60)
        
        if results:
            for i, (page_num, url, email) in enumerate(results, 1):
                print(f"{i:2d}. 页码: {page_num} | 邮箱: {email}")
                print(f"    URL: {url[:80]}...")
                print()
        else:
            print("❌ 没有找到任何邮箱")
        
        # 关闭搜索器
        searcher.close()
        
        print("=" * 60)
        print(f"✅ 搜索完成! 共找到 {len(results)} 个邮箱")
        print("=" * 60)
        
        return results
        
    except Exception as e:
        print(f"❌ 搜索过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return []

def interactive_search():
    """交互式搜索"""
    print("🚀 Google邮箱搜索工具")
    print("=" * 60)
    
    while True:
        try:
            # 获取用户输入
            query = input("\n请输入搜索查询语句 (输入 'quit' 退出): ").strip()
            
            if query.lower() in ['quit', 'exit', 'q']:
                print("👋 再见!")
                break
            
            if not query:
                print("❌ 查询语句不能为空")
                continue
            
            # 询问配置
            print("\n配置选项:")
            headless_input = input("无头模式? (y/n, 默认y): ").strip().lower()
            headless = headless_input != 'n'
            
            proxy_input = input("使用代理? (y/n, 默认n): ").strip().lower()
            use_proxy = proxy_input == 'y'
            
            pages_input = input("最大搜索页数 (默认2): ").strip()
            try:
                max_pages = int(pages_input) if pages_input else 2
            except:
                max_pages = 2
            
            # 执行搜索
            results = single_search_test(query, headless, use_proxy, max_pages)
            
            # 询问是否保存结果
            if results:
                save_input = input(f"\n是否保存结果到文件? (y/n): ").strip().lower()
                if save_input == 'y':
                    save_results_to_file(query, results)
            
        except KeyboardInterrupt:
            print("\n👋 再见!")
            break
        except Exception as e:
            print(f"❌ 出错: {e}")

def save_results_to_file(query, results):
    """保存结果到文件"""
    try:
        import json
        from datetime import datetime
        
        filename = f"search_results_{datetime.now().strftime('%Y%m%d_%H%M%S')}.json"
        
        data = {
            "query": query,
            "timestamp": datetime.now().isoformat(),
            "total_results": len(results),
            "results": [
                {
                    "page": page_num,
                    "url": url,
                    "email": email
                }
                for page_num, url, email in results
            ]
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(data, f, ensure_ascii=False, indent=2)
        
        print(f"✅ 结果已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 保存文件失败: {e}")

def quick_test():
    """快速测试 - 使用预设查询"""
    test_queries = [
        "Beijing restaurant @gmail.com",
        "Shanghai hotel contact email",
        "Guangzhou company email address"
    ]
    
    print("🧪 快速测试模式")
    print("=" * 60)
    
    for i, query in enumerate(test_queries, 1):
        print(f"\n测试 {i}/{len(test_queries)}: {query}")
        results = single_search_test(query, headless=True, use_proxy=False, max_pages=1)
        
        if results:
            print(f"✅ 测试 {i} 成功，找到 {len(results)} 个邮箱")
        else:
            print(f"⚠️ 测试 {i} 没有找到邮箱")
        
        # 测试间隔
        if i < len(test_queries):
            import time
            print("等待 3 秒...")
            time.sleep(3)

def main():
    """主函数"""
    if len(sys.argv) > 1:
        # 命令行模式
        query = " ".join(sys.argv[1:])
        single_search_test(query)
    else:
        # 交互模式
        print("选择模式:")
        print("1. 交互式搜索")
        print("2. 快速测试")
        
        choice = input("请选择 (1/2, 默认1): ").strip()
        
        if choice == '2':
            quick_test()
        else:
            interactive_search()

if __name__ == "__main__":
    main()
