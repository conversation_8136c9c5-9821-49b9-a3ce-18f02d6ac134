# -*- coding: utf-8 -*-
"""
快速搜索测试 - 简单版本
使用方法: python quick_search.py "你的搜索词"
"""
import sys
import os

# 添加上级目录到Python路径，以便导入db模块
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

def quick_search(query):
    """
    快速搜索函数
    
    Args:
        query: 搜索查询语句
    
    Returns:
        list: 搜索结果列表 [(page_num, url, email), ...]
    """
    print(f"🔍 搜索: {query}")
    print("-" * 50)
    
    try:
        # 导入爬虫模块
        from google_spider import GoogleSearcher
        
        # 创建搜索器 (无头模式，不使用代理)
        searcher = GoogleSearcher(
            thread_id=0,
            headless=True,      # 无头模式
            use_proxy=False     # 不使用代理
        )
        
        print("✓ 搜索器初始化完成")
        
        # 执行搜索 (最多搜索2页)
        results = searcher.search(query, max_pages=2)
        
        # 显示结果
        print(f"\n📊 搜索结果: 找到 {len(results)} 个邮箱")
        print("-" * 50)
        
        if results:
            for i, (page_num, url, email) in enumerate(results, 1):
                print(f"{i:2d}. {email}")
                print(f"    页码: {page_num} | URL: {url[:60]}...")
        else:
            print("❌ 没有找到任何邮箱")
        
        # 关闭搜索器
        searcher.close()
        
        print("-" * 50)
        print(f"✅ 搜索完成!")
        
        return results
        
    except Exception as e:
        print(f"❌ 搜索失败: {e}")
        import traceback
        traceback.print_exc()
        return []

def main():
    """主函数"""
    # 检查命令行参数
    if len(sys.argv) < 2:
        print("使用方法:")
        print("  python quick_search.py \"你的搜索词\"")
        print("\n示例:")
        print("  python quick_search.py \"Beijing restaurant email\"")
        print("  python quick_search.py \"Shanghai hotel @gmail.com\"")
        print("  python quick_search.py \"Guangzhou company contact\"")
        
        # 如果没有参数，使用默认搜索词
        query = input("\n或者直接输入搜索词: ").strip()
        if not query:
            print("❌ 搜索词不能为空")
            return
    else:
        # 从命令行参数获取搜索词
        query = " ".join(sys.argv[1:])
    
    # 执行搜索
    results = quick_search(query)
    
    # 如果找到结果，询问是否保存
    if results:
        save_choice = input(f"\n是否保存 {len(results)} 个结果到文件? (y/n): ").strip().lower()
        if save_choice == 'y':
            save_to_file(query, results)

def save_to_file(query, results):
    """保存结果到文件"""
    try:
        from datetime import datetime
        
        # 生成文件名
        timestamp = datetime.now().strftime('%Y%m%d_%H%M%S')
        filename = f"search_{timestamp}.txt"
        
        # 保存结果
        with open(filename, 'w', encoding='utf-8') as f:
            f.write(f"搜索词: {query}\n")
            f.write(f"搜索时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n")
            f.write(f"结果数量: {len(results)}\n")
            f.write("-" * 60 + "\n\n")
            
            for i, (page_num, url, email) in enumerate(results, 1):
                f.write(f"{i:2d}. 邮箱: {email}\n")
                f.write(f"    页码: {page_num}\n")
                f.write(f"    URL: {url}\n\n")
        
        print(f"✅ 结果已保存到: {filename}")
        
    except Exception as e:
        print(f"❌ 保存失败: {e}")

if __name__ == "__main__":
    main()
