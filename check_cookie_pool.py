#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie池功能检查脚本
检查Cookie池功能是否正常工作
"""

import sys
import os
import time

# 添加crawler模块到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'crawler'))

try:
    from crawler.config import CookiePoolConfig, setup_logging
    from crawler.cookie_pool import CookiePool, CookieInfo
    from crawler.pool_based_spider import PoolBasedGoogleSpider
    from crawler.pool_spider_manager import create_pool_manager
    
    print("✓ 所有模块导入成功")
except ImportError as e:
    print(f"✗ 模块导入失败: {e}")
    sys.exit(1)


def check_configuration():
    """检查配置"""
    print("\n" + "="*50)
    print("检查配置")
    print("="*50)
    
    try:
        print(f"✓ 池大小: {CookiePoolConfig.POOL_SIZE}")
        print(f"✓ 失败阈值: {CookiePoolConfig.MAX_FAILURES}")
        print(f"✓ 最大重试Cookie数: {CookiePoolConfig.MAX_RETRY_COOKIES}")
        print(f"✓ 最少活跃Cookie: {CookiePoolConfig.MIN_ACTIVE_COOKIES}")
        print(f"✓ 池存储目录: {CookiePoolConfig.POOL_DIR}")
        print(f"✓ 池状态文件: {CookiePoolConfig.POOL_STATUS_FILE}")
        return True
    except Exception as e:
        print(f"✗ 配置检查失败: {e}")
        return False


def check_cookie_pool():
    """检查Cookie池基本功能"""
    print("\n" + "="*50)
    print("检查Cookie池基本功能")
    print("="*50)
    
    try:
        # 创建小型测试池
        print("创建测试Cookie池...")
        pool = CookiePool(pool_size=2, max_failures=2, pool_dir='test_pool')
        
        # 检查初始状态
        status = pool.get_pool_status()
        print(f"✓ 池创建成功: {status['active_cookies']}/{status['total_cookies']} 活跃Cookie")
        
        # 测试获取Cookie
        cookie = pool.get_available_cookie()
        if cookie:
            print(f"✓ 成功获取Cookie: {cookie.id[:8]}...")
            
            # 测试标记成功
            pool.mark_cookie_success(cookie.id)
            print("✓ 标记Cookie成功")
            
            # 测试标记失败
            pool.mark_cookie_failed(cookie.id)
            print("✓ 标记Cookie失败")
        else:
            print("✗ 无法获取Cookie")
            return False
        
        # 测试状态保存
        pool.save_pool()
        print("✓ 状态保存成功")
        
        return True
        
    except Exception as e:
        print(f"✗ Cookie池检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_spider():
    """检查爬虫功能"""
    print("\n" + "="*50)
    print("检查爬虫功能")
    print("="*50)
    
    try:
        # 创建爬虫实例
        print("创建Cookie池爬虫...")
        spider = PoolBasedGoogleSpider(
            thread_id=0,
            use_cookie_pool=True,
            headless=True,
            use_proxy=False
        )
        
        # 检查Cookie池状态
        status = spider.get_pool_status()
        print(f"✓ 爬虫创建成功，Cookie池: {status['active_cookies']}/{status['total_cookies']} 活跃")
        
        # 测试简单搜索
        print("测试搜索功能...")
        result = spider.search_page("test", page=1)
        
        if result is not None:
            print("✓ 搜索功能正常")
            if result.get('cookie_id'):
                print(f"✓ Cookie轮换正常: {result['cookie_id'][:8]}...")
        else:
            print("✗ 搜索功能异常")
            return False
        
        spider.close()
        print("✓ 爬虫关闭成功")
        
        return True
        
    except Exception as e:
        print(f"✗ 爬虫检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_manager():
    """检查管理器功能"""
    print("\n" + "="*50)
    print("检查管理器功能")
    print("="*50)
    
    try:
        # 创建管理器
        print("创建Cookie池管理器...")
        manager = create_pool_manager(
            num_threads=1,
            batch_size=10,
            use_proxy=False
        )
        
        # 检查状态
        status = manager.get_pool_status()
        print(f"✓ 管理器创建成功")
        print(f"✓ 线程数: {status['num_threads']}")
        print(f"✓ 批处理大小: {status['batch_size']}")
        print(f"✓ 使用Cookie池: {status['use_cookie_pool']}")
        
        if status['use_cookie_pool']:
            pool_status = status['cookie_pool']
            print(f"✓ Cookie池状态: {pool_status['active_cookies']}/{pool_status['total_cookies']} 活跃")
        
        # 测试管理器功能
        print("测试管理器功能...")
        manager.monitor_pool_health()
        print("✓ 池健康监控正常")
        
        manager.cleanup_failed_cookies()
        print("✓ 失效Cookie清理正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 管理器检查失败: {e}")
        import traceback
        traceback.print_exc()
        return False


def check_compatibility():
    """检查向后兼容性"""
    print("\n" + "="*50)
    print("检查向后兼容性")
    print("="*50)
    
    try:
        # 测试传统模式
        print("测试传统单Cookie模式...")
        spider = PoolBasedGoogleSpider(
            thread_id=0,
            use_cookie_pool=False,  # 禁用Cookie池
            headless=True,
            use_proxy=False
        )
        
        status = spider.get_pool_status()
        if 'message' in status:
            print(f"✓ 传统模式正常: {status['message']}")
        else:
            print("✗ 传统模式异常")
            return False
        
        spider.close()
        print("✓ 向后兼容性正常")
        
        return True
        
    except Exception as e:
        print(f"✗ 兼容性检查失败: {e}")
        return False


def run_all_checks():
    """运行所有检查"""
    print("Cookie池功能检查")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    checks = [
        ("配置检查", check_configuration),
        ("Cookie池检查", check_cookie_pool),
        ("爬虫检查", check_spider),
        ("管理器检查", check_manager),
        ("兼容性检查", check_compatibility)
    ]
    
    results = []
    
    for name, check_func in checks:
        try:
            result = check_func()
            results.append((name, result))
        except Exception as e:
            print(f"✗ {name}执行异常: {e}")
            results.append((name, False))
    
    # 显示总结
    print("\n" + "="*50)
    print("检查结果总结")
    print("="*50)
    
    passed = 0
    for name, result in results:
        status = "✓ 通过" if result else "✗ 失败"
        print(f"{name}: {status}")
        if result:
            passed += 1
    
    print(f"\n总计: {passed}/{len(results)} 项检查通过")
    
    if passed == len(results):
        print("🎉 所有检查都通过！Cookie池功能正常。")
        return True
    else:
        print("⚠️  部分检查失败，请检查相关功能。")
        return False


def quick_check():
    """快速检查"""
    print("快速检查Cookie池功能...")
    
    try:
        # 快速创建和测试
        spider = PoolBasedGoogleSpider(use_cookie_pool=True)
        status = spider.get_pool_status()
        spider.close()
        
        if status['active_cookies'] > 0:
            print(f"✓ 快速检查通过，Cookie池有 {status['active_cookies']} 个活跃Cookie")
            return True
        else:
            print("✗ 快速检查失败，没有活跃Cookie")
            return False
            
    except Exception as e:
        print(f"✗ 快速检查失败: {e}")
        return False


def main():
    """主函数"""
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == "quick":
            quick_check()
        elif mode == "config":
            check_configuration()
        elif mode == "pool":
            check_cookie_pool()
        elif mode == "spider":
            check_spider()
        elif mode == "manager":
            check_manager()
        elif mode == "compat":
            check_compatibility()
        else:
            print(f"未知检查模式: {mode}")
            print("可用模式: quick, config, pool, spider, manager, compat")
    else:
        # 运行所有检查
        success = run_all_checks()
        sys.exit(0 if success else 1)


if __name__ == "__main__":
    main()
