# Cookie池管理和查询重试机制

## 概述

本项目新增了Cookie池管理和查询重试机制，实现了以下功能：

1. **Cookie池管理**：维护n个Cookie的池子，Cookie失败m次后才删除
2. **查询重试机制**：查询失败时自动换Cookie重试（最多k个Cookie），只有重试完所有Cookie都失败才认为查询真的失败

## 主要特性

### Cookie池管理
- 🔄 **自动轮换**：智能选择失败次数最少的Cookie
- 📊 **失败追踪**：跟踪每个Cookie的失败次数
- 🧹 **自动清理**：失败次数超过阈值的Cookie自动移除
- 🔧 **动态补充**：活跃Cookie不足时自动添加新Cookie
- 💾 **持久化存储**：Cookie池状态保存到文件
- 🔒 **线程安全**：支持多线程并发访问

### 查询重试机制
- 🔁 **自动重试**：失败时自动切换到下一个可用Cookie
- ⚙️ **可配置重试**：可设置最大重试Cookie数量
- 📈 **智能选择**：优先使用失败次数少的Cookie
- 🎯 **精确失败检测**：区分真正的封禁和临时问题

## 配置参数

### Cookie池配置 (CookiePoolConfig)

```python
# Cookie池大小（维护n个Cookie）
POOL_SIZE = 5

# Cookie失败阈值（失败m次后删除）
MAX_FAILURES = 3

# 查询重试时最多尝试的Cookie数量（最多k个Cookie）
MAX_RETRY_COOKIES = None  # None表示尝试所有可用Cookie

# Cookie池存储目录
POOL_DIR = 'cookie_pool'

# 最少保持的活跃Cookie数量
MIN_ACTIVE_COOKIES = 2

# 池的最大容量
MAX_POOL_SIZE = 10
```

## 使用方法

### 1. 基本使用

```python
from crawler.pool_based_spider import PoolBasedGoogleSpider

# 创建使用Cookie池的爬虫
spider = PoolBasedGoogleSpider(
    thread_id=0,
    use_cookie_pool=True  # 启用Cookie池
)

# 执行搜索（自动处理Cookie轮换和重试）
results = spider.search_all_pages("python programming", max_pages=3)

# 关闭爬虫
spider.close()
```

### 2. 使用爬虫管理器

```python
from crawler.pool_spider_manager import create_pool_manager

# 创建Cookie池管理器
manager = create_pool_manager(
    num_threads=4,
    batch_size=100,
    use_proxy=False
)

# 运行爬虫（带Cookie池监控）
manager.run_with_monitoring()

# 查看状态报告
manager.print_status_report()
```

### 3. 传统模式（向后兼容）

```python
# 仍然可以使用传统单Cookie模式
spider = PoolBasedGoogleSpider(
    use_cookie_pool=False  # 禁用Cookie池
)
```

## 核心类说明

### CookiePool
Cookie池管理器，负责：
- Cookie的创建、存储和管理
- 失败次数追踪和清理
- 线程安全的Cookie分配

### CookieInfo
Cookie信息类，包含：
- Cookie ID和内容
- 失败次数和状态
- 创建时间和最后使用时间

### PoolBasedGoogleSpider
基于Cookie池的爬虫类，提供：
- 自动Cookie轮换
- 智能重试机制
- 向后兼容的接口

### PoolSpiderManager
Cookie池爬虫管理器，支持：
- 多线程共享Cookie池
- 池健康状态监控
- 自动维护和清理

## 工作流程

### 搜索流程
1. 从Cookie池获取可用Cookie（优先选择失败次数少的）
2. 使用Cookie执行搜索请求
3. 根据结果标记Cookie成功或失败
4. 如果失败，自动切换到下一个Cookie重试
5. 重试所有可用Cookie后仍失败才认为真正失败

### 池维护流程
1. 监控活跃Cookie数量
2. 清理失败次数超过阈值的Cookie
3. 当活跃Cookie不足时自动补充
4. 定期保存池状态到文件

## 测试和示例

### 运行测试
```bash
# 综合功能测试
python crawler/test_cookie_pool.py

# 交互式测试
python crawler/test_cookie_pool.py interactive

# 使用示例
python crawler/pool_example.py

# 快速测试
python crawler/pool_example.py quick
```

### 示例脚本
- `test_cookie_pool.py`：完整的功能测试
- `pool_example.py`：使用示例和演示
- `pool_spider_manager.py`：管理器使用示例

## 监控和调试

### 查看Cookie池状态
```python
# 获取详细状态
status = spider.get_pool_status()
print(f"活跃Cookie: {status['active_cookies']}/{status['total_cookies']}")

# 查看Cookie详情
for cookie in status['cookies_detail']:
    print(f"{cookie['id']}: {cookie['status']} (失败{cookie['failure_count']}次)")
```

### 手动操作
```python
# 刷新所有Cookie
spider.refresh_cookie_pool()

# 清理失效Cookie
pool.cleanup_failed_cookies()

# 手动标记Cookie失败
pool.mark_cookie_failed(cookie_id)
```

## 日志记录

系统会记录详细的日志信息：
- Cookie池的创建和初始化
- Cookie的获取、使用和失败
- 自动重试和切换过程
- 池维护和清理操作

## 性能优化

### 建议配置
- **小规模使用**：池大小3-5，失败阈值2-3
- **大规模使用**：池大小5-10，失败阈值3-5
- **高频使用**：启用代理，增加池大小

### 最佳实践
1. 根据使用频率调整池大小
2. 监控Cookie失败率，及时调整阈值
3. 定期清理失效Cookie
4. 使用代理分散请求压力

## 故障排除

### 常见问题
1. **Cookie池初始化失败**：检查网络连接和代理设置
2. **所有Cookie都失败**：可能被大规模封禁，需要更换IP或等待
3. **池大小不足**：增加POOL_SIZE或降低MAX_FAILURES
4. **重试次数过多**：检查MAX_RETRY_COOKIES设置

### 调试技巧
1. 启用详细日志记录
2. 使用交互式测试模式
3. 监控Cookie池状态变化
4. 检查网络和代理连接

## 升级说明

### 从旧版本升级
1. 现有代码无需修改（向后兼容）
2. 可选择性启用Cookie池功能
3. 配置文件会自动扩展新参数
4. 旧的Cookie文件仍然可用

### 迁移建议
1. 先在测试环境验证功能
2. 逐步启用Cookie池模式
3. 监控性能和成功率变化
4. 根据实际情况调整配置参数
