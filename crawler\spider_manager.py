# -*- coding: utf-8 -*-
"""
Spider Manager - 爬虫管理器
"""
import time
import threading
import logging
from datetime import datetime
from sqlalchemy import func
from config import setup_logging, CrawlerConfig
from google_spider import GoogleSearcher
from db.db_models import SearchQueries, SearchResults
from db.init_db import SessionLocal

# 设置日志
setup_logging()
logger = logging.getLogger('SpiderManager')


class GoogleCrawlerManager:
    """Google爬虫管理器"""
    
    def __init__(self, threads=4, batch_size=4, headless=True, use_proxy=False):
        """
        初始化爬虫管理器
        
        Args:
            threads: 线程数
            batch_size: 每批处理的数据量
            headless: 是否无头模式
            use_proxy: 是否使用代理
        """
        self.num_threads = threads
        self.batch_size = batch_size
        self.headless = headless
        self.use_proxy = use_proxy
        
        # 线程管理
        self.result_lock = threading.Lock()
        self.threads = []
        self.thread_queries = []  # 每个线程负责的查询列表
        
        # 进度跟踪
        self.total_queries = 0
        self.completed_queries = 0
        self.start_time = None
        
        logger.info("=" * 50)
        logger.info("Google搜索邮箱爬虫启动")
        logger.info(f"线程数量: {threads}")
        logger.info(f"批处理大小: {batch_size}")
        logger.info(f"无头模式: {headless}")
        logger.info(f"使用代理: {use_proxy}")
        logger.info("=" * 50)
    
    def check_remaining_queries(self):
        """检查是否还有未完成的查询"""
        try:
            db = SessionLocal()
            try:
                count = db.query(func.count(SearchQueries.id)).filter(
                    (SearchQueries.crawled_status.is_(None)) |
                    (SearchQueries.crawled_status != 'completed')
                ).scalar()
                return count > 0
            finally:
                db.close()
        except Exception as e:
            logger.error(f"检查剩余查询失败: {e}")
            return True  # 出错时假设还有查询待处理
    
    def get_uncrawled_queries(self):
        """从数据库获取未爬取的查询，随机选择而非按顺序"""
        try:
            db = SessionLocal()
            try:
                # 获取所有未爬取的查询，使用随机排序
                queries = db.query(SearchQueries).filter(
                    (SearchQueries.crawled_status.is_(None)) |
                    (SearchQueries.crawled_status != 'completed')
                ).order_by(func.random()).limit(self.batch_size).all()
                
                data = []
                for query in queries:
                    if query:
                        data.append((query.id, query.county, query.goods, query.email_type))
                return data
            finally:
                db.close()
        except Exception as e:
            logger.error(f"获取未爬取查询失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def distribute_queries(self):
        """将查询分配给各个线程"""
        try:
            # 获取所有未处理的查询
            unprocessed_queries = self.get_uncrawled_queries()
            
            self.total_queries = len(unprocessed_queries)
            logger.info(f"共有 {self.total_queries} 个查询需要处理")
            
            if self.total_queries == 0:
                return []
            
            # 将查询平均分配给各个线程
            self.thread_queries = [[] for _ in range(self.num_threads)]
            for i, query_data in enumerate(unprocessed_queries):
                thread_idx = i % self.num_threads
                self.thread_queries[thread_idx].append(query_data)
            
            # 打印每个线程分配的查询数量
            for i, queries in enumerate(self.thread_queries):
                if queries:  # 只显示有任务的线程
                    logger.info(f"线程 {i} 分配了 {len(queries)} 个查询")
            
            return self.thread_queries
        except Exception as e:
            logger.error(f"分配查询失败: {e}")
            import traceback
            traceback.print_exc()
            return []
    
    def update_progress(self, thread_id, completed):
        """更新并报告当前进度"""
        with self.result_lock:
            self.completed_queries += completed
            if self.total_queries > 0:
                progress = (self.completed_queries / self.total_queries) * 100
                elapsed_time = time.time() - self.start_time if self.start_time else 0
                
                logger.info(
                    f"[线程 {thread_id}] 完成 {completed} 个查询 | "
                    f"总进度: {self.completed_queries}/{self.total_queries} ({progress:.1f}%) | "
                    f"耗时: {elapsed_time:.1f}s"
                )
    
    def worker(self, thread_id, queries):
        """
        工作线程函数
        
        Args:
            thread_id: 线程ID
            queries: 该线程负责处理的查询列表，每个元素是(id, county, goods, email_type)元组
        """
        if not queries:
            logger.info(f"[线程 {thread_id}] 没有分配到查询任务")
            return
        
        # 为每个线程创建一个GoogleSearcher实例
        searcher = GoogleSearcher(
            thread_id=thread_id,
            headless=self.headless,
            use_proxy=self.use_proxy
        )
        
        completed = 0
        local_results = {}  # 本地存储结果，减少数据库访问 {id: (search_results, success)}
        
        try:
            for query_id, county, goods, email_type in queries:
                try:
                    # 构建查询字符串: county + goods + email_type
                    if email_type == '@email':
                        search_query = f'{county} {goods} {email_type}'
                    else:
                        search_query = f'{county} {goods} "{email_type}"'
                    
                    logger.debug(f"[线程 {thread_id}] 开始搜索: {search_query}")
                    
                    # 执行搜索，获取所有页面的结果
                    search_results = searcher.search(search_query)
                    
                    # 存储结果到本地字典，标记为成功
                    local_results[query_id] = (search_results, True, county, goods, email_type)
                    
                    logger.debug(f"[线程 {thread_id}] 搜索完成: {search_query}，找到 {len(search_results)} 个结果")
                    
                except Exception as e:
                    logger.error(f"[线程 {thread_id}] 处理查询 {county} {goods} {email_type} 时出错: {e}")
                    # 存储空结果，标记为失败
                    local_results[query_id] = ([], False, county, goods, email_type)
                
                completed += 1
                
                # 每处理10个查询更新一次数据库
                if completed % 10 == 0 or completed == len(queries):
                    self.update_results_db(local_results, thread_id)
                    self.update_progress(thread_id, len(local_results))
                    local_results = {}  # 清空本地结果
                
                # 添加随机延迟，避免请求过于频繁
                if completed < len(queries):  # 不是最后一个查询
                    import random
                    delay = random.uniform(*CrawlerConfig.DELAYS['query'])
                    time.sleep(delay)
            
            # 处理剩余的结果
            if local_results:
                self.update_results_db(local_results, thread_id)
                self.update_progress(thread_id, len(local_results))
            
            logger.info(f"[线程 {thread_id}] 完成所有 {len(queries)} 个查询")
            
        except Exception as e:
            logger.error(f"[线程 {thread_id}] 工作线程异常: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 关闭搜索器，释放资源
            searcher.close()

    def update_results_db(self, results_dict, thread_id):
        """
        更新数据库中的查询结果

        Args:
            results_dict: 查询结果字典 {query_id: (search_results, success, county, goods, email_type)}
            thread_id: 线程ID
        """
        if not results_dict:
            return

        try:
            # 使用线程锁保护数据库操作
            with self.result_lock:
                db = SessionLocal()
                try:
                    # 更新结果
                    for query_id, (search_results, success, county, goods, email_type) in results_dict.items():
                        # 更新查询状态，无论成功失败都标记为completed
                        query = db.query(SearchQueries).filter(SearchQueries.id == query_id).first()
                        if query:
                            query.crawled_status = 'completed'

                            # 如果搜索成功且有结果，保存结果到数据库
                            if success and search_results:
                                for page_num, url, email in search_results:
                                    if email:
                                        result = SearchResults(
                                            url=url,
                                            email=email,
                                            current_page=page_num,
                                            city=county,
                                            goods=goods,
                                            email_type=email_type,
                                            date=datetime.now(),
                                        )
                                        db.add(result)

                    db.commit()
                    logger.debug(f"[线程 {thread_id}] 已更新 {len(results_dict)} 个查询结果到数据库")

                except Exception as e:
                    db.rollback()
                    logger.error(f"[线程 {thread_id}] 更新数据库失败: {e}")
                    import traceback
                    traceback.print_exc()
                finally:
                    db.close()

        except Exception as e:
            logger.error(f"[线程 {thread_id}] 更新结果数据库失败: {e}")
            import traceback
            traceback.print_exc()

    def start_batch(self):
        """启动一批爬虫任务"""
        # 分配查询任务给各个线程
        thread_queries = self.distribute_queries()

        if not any(thread_queries):
            logger.info("没有需要处理的新查询")
            return False

        # 重置计数器
        self.completed_queries = 0
        self.start_time = time.time()

        # 创建并启动工作线程
        self.threads = []
        for i in range(self.num_threads):
            if i < len(thread_queries) and thread_queries[i]:
                thread = threading.Thread(target=self.worker, args=(i, thread_queries[i]))
                thread.daemon = True  # 设置为守护线程
                self.threads.append(thread)
                thread.start()
                logger.info(f"启动线程 {i}")

        return True

    def wait_completion(self):
        """等待所有线程完成"""
        for thread in self.threads:
            thread.join()

        elapsed_time = time.time() - self.start_time if self.start_time else 0
        logger.info(f"所有线程已完成! 总耗时: {elapsed_time:.1f}s")

    def run(self):
        """运行爬虫"""
        try:
            total_processed = 0

            # 持续运行直到所有查询都完成
            while self.check_remaining_queries():
                logger.info("开始新一批查询任务...")

                # 启动一批任务
                started = self.start_batch()

                # 如果有任务需要执行
                if started:
                    logger.info("正在执行查询，请稍候...")
                    self.wait_completion()
                    total_processed += self.completed_queries
                    logger.info(f"本批次查询已完成! 处理了 {self.completed_queries} 个查询")

                    # 批次间添加延迟
                    if self.check_remaining_queries():
                        import random
                        delay = random.uniform(*CrawlerConfig.DELAYS['batch'])
                        logger.info(f"等待 {delay:.1f}s 后开始下一批次...")
                        time.sleep(delay)
                else:
                    logger.info("没有需要处理的查询，检查是否全部完成...")
                    # 再次检查以确认是否所有查询都已完成
                    if not self.check_remaining_queries():
                        logger.info("所有查询已完成!")
                        break
                    else:
                        logger.info("仍有查询需要处理，但当前无法获取，稍后重试...")
                        time.sleep(5)  # 等待一段时间后重试

            logger.info("=" * 50)
            logger.info(f"爬虫任务完成! 共处理了 {total_processed} 个查询")
            logger.info("=" * 50)

        except KeyboardInterrupt:
            logger.info("收到中断信号，正在停止爬虫...")
        except Exception as e:
            logger.error(f"爬虫运行出错: {e}")
            import traceback
            traceback.print_exc()
