# Google搜索邮箱爬虫

一个高效的Google搜索邮箱提取工具，支持多线程并发、智能Cookie管理和代理轮换。

## 🚀 快速开始

### 1. 单次搜索测试

最简单的使用方式，输入搜索词即可：

```bash
# 进入crawler目录
cd crawler

# 命令行方式
python quick_search.py "Beijing restaurant email"

# 交互式方式
python quick_search.py
```

### 2. 完整爬虫运行

配置参数并启动完整的多线程爬虫：

```bash
# 进入crawler目录
cd crawler

# 编辑 main.py 中的配置
threads = 4          # 线程数量
batch = 4           # 批处理大小
headless = True     # 无头模式
use_proxy = False   # 是否使用代理

# 运行
python main.py
```

## 📁 项目结构

```
├── crawler/                # 爬虫核心模块 (主要工作目录)
│   ├── main.py            # 主程序入口
│   ├── quick_search.py    # 单次搜索测试
│   ├── config.py          # 配置管理
│   ├── cookie_manager.py  # Cookie管理
│   ├── proxy_manager.py   # 代理管理
│   ├── google_spider.py   # 搜索引擎
│   ├── spider_manager.py  # 爬虫管理器
│   └── cookies/           # Cookie存储目录
├── db/                     # 数据库模块
│   ├── db_models.py       # 数据模型
│   └── init_db.py         # 数据库初始化
└── README.md              # 说明文档
```

## ⚙️ 配置说明

### 基本配置 (main.py)

```python
# 线程配置
threads = 4          # 并发线程数，建议2-8
batch = 4           # 每批处理的查询数量

# Cookie配置
headless = True     # True=无界面，False=显示浏览器
                   # 首次运行建议设为False观察过程

# 代理配置  
use_proxy = False   # True=使用代理，False=直连
                   # 建议先用False测试，稳定后再开启代理
```

### 高级配置 (crawler/config.py)

```python
# 延迟配置
DELAYS = {
    'retry': (1, 2),        # 重试间隔
    'page': (1, 3),         # 页面间隔  
    'query': (0.5, 1.5),    # 查询间隔
    'batch': (2, 5)         # 批次间隔
}

# 请求配置
REQUEST_TIMEOUT = 10        # 请求超时时间
MAX_RETRIES = 3            # 最大重试次数
```

## 🔧 核心特性

### 1. 智能Cookie管理
- **长期使用策略**: Cookie会被使用到真正被封禁才更换
- **自动检测封禁**: 只有检测到明确的封禁页面才刷新Cookie
- **多线程Cookie**: 每个线程使用独立的Cookie文件

### 2. 高效邮箱提取
- **多种格式支持**: 标准邮箱、带空格邮箱、特殊格式
- **智能验证**: 自动验证邮箱格式的有效性
- **去重处理**: 自动去除重复的邮箱地址

### 3. 代理管理
- **多API支持**: 支持多个代理服务商
- **智能切换**: 基于成功率和响应时间选择最佳代理
- **故障转移**: 代理失效时自动切换

### 4. 反检测机制
- **人类行为模拟**: 随机延迟、鼠标移动、页面滚动
- **请求头随机化**: 随机User-Agent和请求头
- **智能重试**: 指数退避重试策略

## 📊 使用示例

### 示例1: 快速测试
```bash
cd crawler
python quick_search.py "Shanghai hotel contact email"
```

### 示例2: 批量搜索
```bash
cd crawler
# 在数据库中添加查询任务，然后运行
python main.py
```

### 示例3: 交互式搜索
```bash
cd crawler
python quick_search.py
# 然后按提示输入搜索词
```

## 🛠️ 安装依赖

```bash
pip install -r requirements.txt
```

主要依赖：
- requests: HTTP请求
- lxml: HTML解析
- playwright: 浏览器自动化
- sqlalchemy: 数据库ORM
- pymysql: MySQL驱动

## 📝 注意事项

1. **Cookie策略**: 系统会尽可能长时间使用同一个Cookie，只有在真正被Google封禁时才会重新获取

2. **请求频率**: 内置了智能延迟机制，避免请求过于频繁被检测

3. **代理使用**: 首次使用建议关闭代理，测试稳定后再开启

4. **数据库配置**: 确保数据库连接信息正确配置在环境变量中

5. **浏览器依赖**: 首次运行需要安装Playwright浏览器：
   ```bash
   playwright install chromium
   ```

## 🔍 故障排除

### 常见问题

1. **Cookie获取失败**
   - 检查网络连接
   - 尝试关闭代理
   - 设置headless=False观察浏览器行为

2. **搜索结果为空**
   - 检查搜索词是否合适
   - 确认Cookie是否有效
   - 查看日志文件了解详细错误

3. **代理连接失败**
   - 检查代理API配置
   - 验证代理服务是否正常
   - 尝试更换代理服务商

## 📈 性能优化

1. **线程数调整**: 根据机器性能调整线程数，建议2-8个
2. **批处理大小**: 根据数据库性能调整批处理大小
3. **延迟配置**: 根据目标网站的限制调整延迟时间
4. **代理质量**: 使用高质量的代理服务提高成功率

## 📄 许可证

本项目仅供学习和研究使用，请遵守相关法律法规和网站的使用条款。
