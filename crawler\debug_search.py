# -*- coding: utf-8 -*-
"""
调试搜索工具 - 用于诊断搜索问题
"""
import sys
import os
import logging

# 添加上级目录到Python路径
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

# 设置详细的调试日志
logging.basicConfig(
    level=logging.DEBUG,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s',
    handlers=[
        logging.FileHandler('debug_search.log'),
        logging.StreamHandler()
    ]
)

def debug_search(query):
    """
    调试搜索函数 - 提供详细的诊断信息
    """
    print("🔍 调试搜索模式")
    print("=" * 60)
    print(f"搜索词: {query}")
    print("=" * 60)
    
    try:
        from google_spider import GoogleSearcher
        
        # 创建搜索器
        searcher = GoogleSearcher(
            thread_id=0,
            headless=False,  # 显示浏览器，便于观察
            use_proxy=False
        )
        
        print("✓ 搜索器初始化完成")
        
        # 先检查Cookie是否有效
        cookies = searcher.cookie_manager.get_valid_cookies()
        print(f"📋 当前Cookie数量: {len(cookies)}")
        
        if cookies:
            print("Cookie示例:")
            for i, (key, value) in enumerate(list(cookies.items())[:3]):
                print(f"  {key}: {value[:20]}...")
        else:
            print("⚠️ 没有Cookie，将自动获取")
        
        print("\n🔍 开始搜索...")
        print("-" * 60)
        
        # 执行单页搜索进行调试
        page_result = searcher.search_page(query, page_num=0)
        
        print(f"\n📊 搜索结果分析:")
        print(f"  - 找到结果数: {len(page_result['results'])}")
        print(f"  - 有下一页: {page_result['has_next']}")
        print(f"  - 错误计数: {page_result['error_count']}")
        print(f"  - 使用代理: {page_result['proxy']}")
        
        if page_result['results']:
            print(f"\n📧 找到的邮箱:")
            for i, (url, email) in enumerate(page_result['results'], 1):
                print(f"  {i}. {email}")
                print(f"     URL: {url[:80]}...")
        else:
            print("\n❌ 没有找到邮箱")
            print("\n🔧 可能的原因:")
            print("  1. 搜索词确实没有相关结果")
            print("  2. Cookie已失效")
            print("  3. 被Google检测")
            print("  4. 页面结构发生变化")
            print("  5. 网络连接问题")
            
            # 提供解决建议
            print("\n💡 解决建议:")
            print("  1. 尝试更换搜索词")
            print("  2. 删除Cookie文件重新获取")
            print("  3. 检查网络连接")
            print("  4. 尝试使用代理")
        
        # 关闭搜索器
        searcher.close()
        
        return page_result['results']
        
    except Exception as e:
        print(f"❌ 搜索过程中出错: {e}")
        import traceback
        traceback.print_exc()
        return []

def test_cookie_validity():
    """测试Cookie有效性"""
    print("\n🍪 Cookie有效性测试")
    print("-" * 40)
    
    try:
        from cookie_manager import GoogleCookieManager
        
        cookie_manager = GoogleCookieManager(
            cookie_file="google_cookies_0.json",
            headless=False,
            use_proxy=False
        )
        
        cookies = cookie_manager.get_valid_cookies()
        
        if cookies:
            print(f"✓ 找到 {len(cookies)} 个Cookie")
            
            # 检查必需的Cookie
            from config import CookieConfig
            missing_cookies = []
            for required in CookieConfig.REQUIRED_COOKIES:
                if required not in cookies:
                    missing_cookies.append(required)
            
            if missing_cookies:
                print(f"⚠️ 缺少必需的Cookie: {missing_cookies}")
            else:
                print("✓ 所有必需的Cookie都存在")
                
            # 测试Cookie是否真的有效
            print("\n🧪 测试Cookie实际效果...")
            import requests
            from config import CrawlerConfig
            
            headers = CrawlerConfig.BASE_HEADERS.copy()
            headers['user-agent'] = CrawlerConfig.USER_AGENTS[0]
            
            response = requests.get(
                'https://www.google.com/search',
                headers=headers,
                cookies=cookies,
                params={'q': 'test'},
                timeout=10
            )
            
            if response.status_code == 200:
                if 'id="search"' in response.text:
                    print("✅ Cookie测试成功，可以正常搜索")
                else:
                    print("⚠️ Cookie可能已失效，建议重新获取")
            else:
                print(f"❌ 请求失败，状态码: {response.status_code}")
                
        else:
            print("❌ 没有找到Cookie")
            
    except Exception as e:
        print(f"❌ Cookie测试失败: {e}")

def main():
    """主函数"""
    if len(sys.argv) < 2:
        print("调试搜索工具")
        print("=" * 40)
        print("使用方法:")
        print("  python debug_search.py \"搜索词\"")
        print("\n示例:")
        print("  python debug_search.py \"Boyd Electrical @email\"")
        
        # 交互模式
        print("\n选择操作:")
        print("1. 调试搜索")
        print("2. 测试Cookie")
        
        choice = input("请选择 (1/2): ").strip()
        
        if choice == '2':
            test_cookie_validity()
        else:
            query = input("请输入搜索词: ").strip()
            if query:
                debug_search(query)
    else:
        query = " ".join(sys.argv[1:])
        debug_search(query)

if __name__ == "__main__":
    main()
