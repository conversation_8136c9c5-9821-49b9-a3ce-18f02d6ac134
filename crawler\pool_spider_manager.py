# -*- coding: utf-8 -*-
"""
基于Cookie池的爬虫管理器
管理多线程爬虫任务，使用共享Cookie池
"""

import time
import threading
import logging
from typing import List, Dict, Any

from .config import CrawlerConfig, CookiePoolConfig
from .pool_based_spider import PoolBasedGoogleSpider
from .cookie_pool import CookiePool
from .spider_manager import SpiderManager

logger = logging.getLogger(__name__)


class PoolSpiderManager(SpiderManager):
    """基于Cookie池的爬虫管理器"""
    
    def __init__(self, num_threads=4, batch_size=100, use_proxy=False, use_cookie_pool=True):
        """
        初始化管理器
        
        Args:
            num_threads: 线程数量
            batch_size: 批处理大小
            use_proxy: 是否使用代理
            use_cookie_pool: 是否使用Cookie池
        """
        super().__init__(num_threads, batch_size, use_proxy)
        
        self.use_cookie_pool = use_cookie_pool
        
        # 如果使用Cookie池，创建共享的Cookie池
        if use_cookie_pool:
            self.shared_cookie_pool = CookiePool()
            logger.info("创建共享Cookie池")
        else:
            self.shared_cookie_pool = None
            logger.info("使用传统单Cookie模式")
        
        # 池状态监控
        self.pool_stats = {
            'total_requests': 0,
            'successful_requests': 0,
            'failed_requests': 0,
            'cookie_switches': 0
        }
    
    def worker(self, thread_id, queries):
        """
        工作线程函数（重写以支持Cookie池）
        
        Args:
            thread_id: 线程ID
            queries: 查询列表
        """
        logger.info(f"[线程 {thread_id}] 开始处理 {len(queries)} 个查询")
        
        # 创建爬虫实例
        if self.use_cookie_pool:
            # 使用共享Cookie池
            searcher = PoolBasedGoogleSpider(
                thread_id=thread_id,
                headless=True,
                use_proxy=self.use_proxy,
                use_cookie_pool=True
            )
            # 设置共享Cookie池
            searcher.cookie_pool = self.shared_cookie_pool
        else:
            # 使用传统模式
            searcher = PoolBasedGoogleSpider(
                thread_id=thread_id,
                headless=True,
                use_proxy=self.use_proxy,
                use_cookie_pool=False
            )
        
        try:
            local_results = {}
            completed = 0
            
            for query_id, county, goods, email_type in queries:
                try:
                    # 构建搜索关键词
                    keyword = f"{county} {goods} {email_type}"
                    
                    logger.info(f"[线程 {thread_id}] 开始搜索: {keyword}")
                    
                    # 记录请求统计
                    self.pool_stats['total_requests'] += 1
                    
                    # 执行搜索
                    results = searcher.search_all_pages(keyword, max_pages=3)
                    
                    if results:
                        # 搜索成功
                        local_results[query_id] = results
                        self.pool_stats['successful_requests'] += 1
                        
                        logger.info(f"[线程 {thread_id}] 搜索成功: {keyword}, 获取 {len(results)} 个结果")
                    else:
                        # 搜索失败
                        self.pool_stats['failed_requests'] += 1
                        logger.warning(f"[线程 {thread_id}] 搜索失败: {keyword}")
                    
                except Exception as e:
                    logger.error(f"[线程 {thread_id}] 处理查询 {query_id} 时出错: {e}")
                    self.pool_stats['failed_requests'] += 1
                
                completed += 1
                
                # 每处理10个查询更新一次数据库
                if completed % 10 == 0 or completed == len(queries):
                    self.update_results_db(local_results, thread_id)
                    self.update_progress(thread_id, len(local_results))
                    local_results = {}
                
                # 添加随机延迟
                if completed < len(queries):
                    import random
                    delay = random.uniform(*CrawlerConfig.DELAYS['query'])
                    time.sleep(delay)
            
            # 处理剩余结果
            if local_results:
                self.update_results_db(local_results, thread_id)
                self.update_progress(thread_id, len(local_results))
            
            logger.info(f"[线程 {thread_id}] 完成所有 {len(queries)} 个查询")
            
        except Exception as e:
            logger.error(f"[线程 {thread_id}] 工作线程异常: {e}")
            import traceback
            traceback.print_exc()
        finally:
            # 关闭搜索器
            searcher.close()
    
    def get_pool_status(self) -> Dict[str, Any]:
        """获取Cookie池和管理器状态"""
        status = {
            'manager_stats': self.pool_stats.copy(),
            'use_cookie_pool': self.use_cookie_pool,
            'num_threads': self.num_threads,
            'batch_size': self.batch_size
        }
        
        if self.use_cookie_pool and self.shared_cookie_pool:
            status['cookie_pool'] = self.shared_cookie_pool.get_pool_status()
        else:
            status['cookie_pool'] = {'message': '未使用Cookie池模式'}
        
        return status
    
    def refresh_cookie_pool(self):
        """刷新Cookie池"""
        if self.use_cookie_pool and self.shared_cookie_pool:
            logger.info("开始刷新共享Cookie池...")
            self.shared_cookie_pool.refresh_all_cookies()
            logger.info("共享Cookie池刷新完成")
        else:
            logger.warning("未使用Cookie池模式，无法刷新")
    
    def cleanup_failed_cookies(self):
        """清理失效的Cookie"""
        if self.use_cookie_pool and self.shared_cookie_pool:
            logger.info("开始清理失效Cookie...")
            self.shared_cookie_pool.cleanup_failed_cookies()
            logger.info("失效Cookie清理完成")
        else:
            logger.warning("未使用Cookie池模式，无法清理")
    
    def monitor_pool_health(self):
        """监控Cookie池健康状态"""
        if not self.use_cookie_pool or not self.shared_cookie_pool:
            return
        
        status = self.shared_cookie_pool.get_pool_status()
        active_count = status['active_cookies']
        total_count = status['total_cookies']
        
        logger.info(f"Cookie池状态: {active_count}/{total_count} 活跃")
        
        # 如果活跃Cookie太少，触发补充
        if active_count < CookiePoolConfig.MIN_ACTIVE_COOKIES:
            logger.warning(f"活跃Cookie不足({active_count})，触发补充机制")
            self.shared_cookie_pool._try_add_new_cookie()
        
        # 如果失败率太高，触发清理
        failed_count = status['failed_cookies']
        if failed_count > CookiePoolConfig.POOL_SIZE // 2:
            logger.warning(f"失效Cookie过多({failed_count})，触发清理机制")
            self.cleanup_failed_cookies()
    
    def run_with_monitoring(self):
        """运行爬虫并监控Cookie池状态"""
        if not self.use_cookie_pool:
            # 如果不使用Cookie池，回退到原始方法
            return super().run()
        
        logger.info("启动基于Cookie池的爬虫管理器")
        
        # 启动监控线程
        monitor_thread = threading.Thread(target=self._pool_monitor_worker, daemon=True)
        monitor_thread.start()
        
        try:
            # 运行主要爬虫逻辑
            super().run()
        finally:
            logger.info("爬虫任务完成")
    
    def _pool_monitor_worker(self):
        """Cookie池监控工作线程"""
        while True:
            try:
                self.monitor_pool_health()
                
                # 每5分钟检查一次
                time.sleep(300)
                
            except Exception as e:
                logger.error(f"Cookie池监控异常: {e}")
                time.sleep(60)  # 出错后等待1分钟再重试
    
    def print_status_report(self):
        """打印状态报告"""
        status = self.get_pool_status()
        
        print("\n" + "="*50)
        print("Cookie池爬虫管理器状态报告")
        print("="*50)
        
        # 管理器统计
        stats = status['manager_stats']
        print(f"总请求数: {stats['total_requests']}")
        print(f"成功请求: {stats['successful_requests']}")
        print(f"失败请求: {stats['failed_requests']}")
        
        if stats['total_requests'] > 0:
            success_rate = stats['successful_requests'] / stats['total_requests'] * 100
            print(f"成功率: {success_rate:.1f}%")
        
        # Cookie池状态
        if status['use_cookie_pool']:
            pool_status = status['cookie_pool']
            print(f"\nCookie池状态:")
            print(f"总Cookie数: {pool_status['total_cookies']}")
            print(f"活跃Cookie: {pool_status['active_cookies']}")
            print(f"失效Cookie: {pool_status['failed_cookies']}")
            
            print(f"\nCookie详情:")
            for cookie in pool_status['cookies_detail']:
                print(f"  {cookie['id']}: {cookie['status']} (失败{cookie['failure_count']}次)")
        else:
            print(f"\n模式: 传统单Cookie模式")
        
        print("="*50 + "\n")


def create_pool_manager(num_threads=4, batch_size=100, use_proxy=False):
    """
    创建Cookie池管理器的便捷函数
    
    Args:
        num_threads: 线程数量
        batch_size: 批处理大小
        use_proxy: 是否使用代理
        
    Returns:
        PoolSpiderManager: 管理器实例
    """
    return PoolSpiderManager(
        num_threads=num_threads,
        batch_size=batch_size,
        use_proxy=use_proxy,
        use_cookie_pool=True
    )
