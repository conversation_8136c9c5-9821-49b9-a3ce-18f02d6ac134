# -*- coding: utf-8 -*-
"""
Cookie池管理模块
实现Cookie池的创建、管理、轮换和失效处理
"""

import json
import os
import time
import uuid
import threading
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass, asdict
from datetime import datetime

from config import CookiePoolConfig, CookieConfig
from cookie_manager import GoogleCookieManager

logger = logging.getLogger(__name__)


@dataclass
class CookieInfo:
    """Cookie信息类"""
    id: str
    cookies: Dict[str, str]
    failure_count: int = 0
    last_used: float = 0
    created_at: float = 0
    status: str = 'active'  # active, failed, expired
    
    def __post_init__(self):
        if self.created_at == 0:
            self.created_at = time.time()
        if self.last_used == 0:
            self.last_used = time.time()
    
    def to_dict(self):
        """转换为字典"""
        return asdict(self)
    
    @classmethod
    def from_dict(cls, data):
        """从字典创建实例"""
        return cls(**data)
    
    def is_valid(self):
        """检查Cookie是否有效"""
        return (self.status == 'active' and 
                self.failure_count < CookiePoolConfig.MAX_FAILURES and
                bool(self.cookies))
    
    def mark_failed(self):
        """标记失败"""
        self.failure_count += 1
        if self.failure_count >= CookiePoolConfig.MAX_FAILURES:
            self.status = 'failed'
        logger.debug(f"Cookie {self.id} 失败次数: {self.failure_count}")
    
    def mark_success(self):
        """标记成功使用"""
        self.last_used = time.time()
        # 成功使用后可以减少失败计数（可选）
        if self.failure_count > 0:
            self.failure_count = max(0, self.failure_count - 1)
    
    def reset_failures(self):
        """重置失败计数"""
        self.failure_count = 0
        if self.status == 'failed':
            self.status = 'active'


class CookiePool:
    """Cookie池管理器"""
    
    def __init__(self, pool_size=None, max_failures=None, pool_dir=None):
        """
        初始化Cookie池
        
        Args:
            pool_size: 池大小
            max_failures: 最大失败次数
            pool_dir: 池存储目录
        """
        self.pool_size = pool_size or CookiePoolConfig.POOL_SIZE
        self.max_failures = max_failures or CookiePoolConfig.MAX_FAILURES
        self.pool_dir = pool_dir or CookiePoolConfig.POOL_DIR
        
        # Cookie存储
        self.cookies: Dict[str, CookieInfo] = {}
        
        # 线程锁
        self.lock = threading.RLock()
        
        # 确保目录存在
        os.makedirs(self.pool_dir, exist_ok=True)
        
        # 状态文件路径
        self.status_file = os.path.join(self.pool_dir, CookiePoolConfig.POOL_STATUS_FILE)
        
        # 加载现有Cookie
        self.load_pool()
        
        # 如果池为空或Cookie不足，初始化
        if len(self.get_active_cookies()) < CookiePoolConfig.MIN_ACTIVE_COOKIES:
            self.initialize_pool()
        
        logger.info(f"Cookie池初始化完成，当前活跃Cookie数量: {len(self.get_active_cookies())}")
    
    def load_pool(self):
        """从文件加载Cookie池"""
        if os.path.exists(self.status_file):
            try:
                with open(self.status_file, 'r', encoding='utf-8') as f:
                    data = json.load(f)
                
                for cookie_data in data.get('cookies', []):
                    cookie_info = CookieInfo.from_dict(cookie_data)
                    self.cookies[cookie_info.id] = cookie_info
                
                logger.info(f"从文件加载了 {len(self.cookies)} 个Cookie")
                
            except Exception as e:
                logger.error(f"加载Cookie池失败: {e}")
                self.cookies = {}
    
    def save_pool(self):
        """保存Cookie池到文件"""
        try:
            data = {
                'updated_at': time.time(),
                'pool_size': self.pool_size,
                'cookies': [cookie.to_dict() for cookie in self.cookies.values()]
            }
            
            with open(self.status_file, 'w', encoding='utf-8') as f:
                json.dump(data, f, indent=2, ensure_ascii=False)
                
        except Exception as e:
            logger.error(f"保存Cookie池失败: {e}")
    
    def initialize_pool(self):
        """初始化Cookie池"""
        logger.info("开始初始化Cookie池...")
        
        target_count = max(self.pool_size, CookiePoolConfig.MIN_ACTIVE_COOKIES)
        current_active = len(self.get_active_cookies())
        
        for i in range(target_count - current_active):
            try:
                cookie_id = str(uuid.uuid4())
                logger.info(f"创建第 {i+1} 个Cookie: {cookie_id}")
                
                # 创建Cookie管理器
                cookie_manager = GoogleCookieManager(
                    cookie_file=f"pool_cookie_{i}.json",
                    headless=True,
                    use_proxy=False
                )
                
                # 获取新Cookie
                cookies = cookie_manager.create_cookies()
                
                if cookies:
                    cookie_info = CookieInfo(
                        id=cookie_id,
                        cookies=cookies,
                        status='active'
                    )
                    
                    with self.lock:
                        self.cookies[cookie_id] = cookie_info
                    
                    # 保存单个Cookie文件
                    cookie_file = os.path.join(self.pool_dir, f"{cookie_id}.json")
                    with open(cookie_file, 'w', encoding='utf-8') as f:
                        json.dump(cookies, f, indent=2, ensure_ascii=False)
                    
                    logger.info(f"成功创建Cookie: {cookie_id}")
                else:
                    logger.warning(f"创建Cookie失败: {cookie_id}")
                
                # 添加延迟避免被检测
                time.sleep(2)
                
            except Exception as e:
                logger.error(f"初始化Cookie时出错: {e}")
        
        # 保存池状态
        self.save_pool()
        logger.info(f"Cookie池初始化完成，共 {len(self.get_active_cookies())} 个活跃Cookie")
    
    def get_active_cookies(self) -> List[CookieInfo]:
        """获取所有活跃的Cookie"""
        with self.lock:
            return [cookie for cookie in self.cookies.values() if cookie.is_valid()]
    
    def get_available_cookie(self) -> Optional[CookieInfo]:
        """获取一个可用的Cookie（优先使用失败次数少的）"""
        active_cookies = self.get_active_cookies()
        
        if not active_cookies:
            logger.warning("没有可用的Cookie")
            return None
        
        # 按失败次数和最后使用时间排序
        active_cookies.sort(key=lambda x: (x.failure_count, x.last_used))
        
        selected = active_cookies[0]
        logger.debug(f"选择Cookie: {selected.id}, 失败次数: {selected.failure_count}")
        
        return selected
    
    def mark_cookie_failed(self, cookie_id: str):
        """标记Cookie失败"""
        with self.lock:
            if cookie_id in self.cookies:
                self.cookies[cookie_id].mark_failed()
                logger.info(f"标记Cookie失败: {cookie_id}, 失败次数: {self.cookies[cookie_id].failure_count}")
                
                # 如果Cookie失效，尝试添加新的
                if not self.cookies[cookie_id].is_valid():
                    self._try_add_new_cookie()
                
                self.save_pool()
    
    def mark_cookie_success(self, cookie_id: str):
        """标记Cookie成功"""
        with self.lock:
            if cookie_id in self.cookies:
                self.cookies[cookie_id].mark_success()
                self.save_pool()
    
    def _try_add_new_cookie(self):
        """尝试添加新Cookie（如果需要）"""
        active_count = len(self.get_active_cookies())
        
        if active_count < CookiePoolConfig.MIN_ACTIVE_COOKIES:
            logger.info(f"活跃Cookie不足({active_count})，尝试添加新Cookie")
            
            try:
                cookie_id = str(uuid.uuid4())
                cookie_manager = GoogleCookieManager(
                    cookie_file=f"pool_cookie_new_{int(time.time())}.json",
                    headless=True,
                    use_proxy=False
                )
                
                cookies = cookie_manager.create_cookies()
                
                if cookies:
                    cookie_info = CookieInfo(
                        id=cookie_id,
                        cookies=cookies,
                        status='active'
                    )
                    
                    self.cookies[cookie_id] = cookie_info
                    
                    # 保存Cookie文件
                    cookie_file = os.path.join(self.pool_dir, f"{cookie_id}.json")
                    with open(cookie_file, 'w', encoding='utf-8') as f:
                        json.dump(cookies, f, indent=2, ensure_ascii=False)
                    
                    logger.info(f"成功添加新Cookie: {cookie_id}")
                    return True
                    
            except Exception as e:
                logger.error(f"添加新Cookie失败: {e}")
        
        return False
    
    def get_pool_status(self) -> Dict:
        """获取池状态信息"""
        with self.lock:
            active_cookies = self.get_active_cookies()
            failed_cookies = [c for c in self.cookies.values() if c.status == 'failed']
            
            return {
                'total_cookies': len(self.cookies),
                'active_cookies': len(active_cookies),
                'failed_cookies': len(failed_cookies),
                'pool_size': self.pool_size,
                'max_failures': self.max_failures,
                'cookies_detail': [
                    {
                        'id': cookie.id[:8] + '...',
                        'status': cookie.status,
                        'failure_count': cookie.failure_count,
                        'last_used': datetime.fromtimestamp(cookie.last_used).strftime('%Y-%m-%d %H:%M:%S')
                    }
                    for cookie in self.cookies.values()
                ]
            }
    
    def cleanup_failed_cookies(self):
        """清理失效的Cookie"""
        with self.lock:
            failed_ids = [
                cookie_id for cookie_id, cookie in self.cookies.items()
                if not cookie.is_valid()
            ]
            
            for cookie_id in failed_ids:
                # 删除Cookie文件
                cookie_file = os.path.join(self.pool_dir, f"{cookie_id}.json")
                if os.path.exists(cookie_file):
                    os.remove(cookie_file)
                
                # 从池中移除
                del self.cookies[cookie_id]
                logger.info(f"清理失效Cookie: {cookie_id}")
            
            if failed_ids:
                self.save_pool()
                logger.info(f"清理了 {len(failed_ids)} 个失效Cookie")
    
    def refresh_all_cookies(self):
        """刷新所有Cookie（强制重新获取）"""
        logger.info("开始刷新所有Cookie...")
        
        with self.lock:
            # 清空现有Cookie
            self.cookies.clear()
        
        # 重新初始化
        self.initialize_pool()
        
        logger.info("所有Cookie刷新完成")
