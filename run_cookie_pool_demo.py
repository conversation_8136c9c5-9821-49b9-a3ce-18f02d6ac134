#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
Cookie池功能演示脚本
快速演示新的Cookie池管理和查询重试机制
"""

import sys
import os
import time

# 添加crawler模块到路径
sys.path.insert(0, os.path.join(os.path.dirname(__file__), 'crawler'))

from crawler.config import setup_logging, CookiePoolConfig
from crawler.pool_based_spider import PoolBasedGoogleSpider
from crawler.pool_spider_manager import create_pool_manager

# 设置日志
setup_logging()


def demo_basic_usage():
    """演示基本使用方法"""
    print("="*60)
    print("Cookie池基本使用演示")
    print("="*60)
    
    print("1. 创建使用Cookie池的爬虫...")
    spider = PoolBasedGoogleSpider(
        thread_id=0,
        use_cookie_pool=True,
        headless=True,
        use_proxy=False
    )
    
    try:
        # 显示初始状态
        status = spider.get_pool_status()
        print(f"   Cookie池状态: {status['active_cookies']}/{status['total_cookies']} 活跃Cookie")
        
        # 执行搜索测试
        print("\n2. 执行搜索测试...")
        test_keywords = ["python tutorial", "web scraping"]
        
        for keyword in test_keywords:
            print(f"   搜索: {keyword}")
            
            # 搜索第一页
            result = spider.search_page(keyword, page=1)
            
            if result['results']:
                print(f"   ✓ 成功获取 {len(result['results'])} 个结果")
                if result.get('cookie_id'):
                    print(f"   使用Cookie: {result['cookie_id'][:8]}...")
            else:
                print(f"   ✗ 搜索失败")
            
            time.sleep(1)  # 添加延迟
        
        # 显示最终状态
        print("\n3. 最终Cookie池状态:")
        final_status = spider.get_pool_status()
        for cookie in final_status['cookies_detail']:
            print(f"   {cookie['id']}: {cookie['status']} (失败{cookie['failure_count']}次)")
    
    finally:
        spider.close()
        print("\n✓ 演示完成")


def demo_failure_recovery():
    """演示失败恢复机制"""
    print("\n" + "="*60)
    print("Cookie失败恢复机制演示")
    print("="*60)
    
    spider = PoolBasedGoogleSpider(use_cookie_pool=True)
    
    try:
        pool = spider.cookie_pool
        
        print("1. 查看初始Cookie池状态...")
        status = pool.get_pool_status()
        print(f"   活跃Cookie: {status['active_cookies']}")
        
        # 模拟Cookie失败
        print("\n2. 模拟Cookie失败...")
        active_cookies = pool.get_active_cookies()
        
        if active_cookies:
            test_cookie = active_cookies[0]
            print(f"   测试Cookie: {test_cookie.id[:8]}...")
            
            # 让Cookie失败几次
            for i in range(2):
                pool.mark_cookie_failed(test_cookie.id)
                print(f"   失败 {i+1} 次，状态: {test_cookie.status}")
        
        print("\n3. 测试自动Cookie切换...")
        result = spider.search_page("test query", page=1)
        
        if result.get('cookie_id'):
            print(f"   ✓ 自动切换成功，使用Cookie: {result['cookie_id'][:8]}...")
        else:
            print("   ✗ 自动切换失败")
        
        print("\n4. 清理失效Cookie...")
        pool.cleanup_failed_cookies()
        
        final_status = pool.get_pool_status()
        print(f"   清理后活跃Cookie: {final_status['active_cookies']}")
    
    finally:
        spider.close()
        print("\n✓ 演示完成")


def demo_configuration():
    """演示配置选项"""
    print("\n" + "="*60)
    print("配置选项演示")
    print("="*60)
    
    print("当前Cookie池配置:")
    print(f"  池大小: {CookiePoolConfig.POOL_SIZE}")
    print(f"  失败阈值: {CookiePoolConfig.MAX_FAILURES}")
    print(f"  最大重试Cookie数: {CookiePoolConfig.MAX_RETRY_COOKIES}")
    print(f"  最少活跃Cookie: {CookiePoolConfig.MIN_ACTIVE_COOKIES}")
    print(f"  池存储目录: {CookiePoolConfig.POOL_DIR}")
    
    print("\n配置说明:")
    print("  - 池大小: 同时维护的Cookie数量")
    print("  - 失败阈值: Cookie失败多少次后被移除")
    print("  - 最大重试Cookie数: 单次查询最多尝试几个Cookie")
    print("  - 最少活跃Cookie: 池中至少保持的可用Cookie数")


def demo_manager():
    """演示管理器功能"""
    print("\n" + "="*60)
    print("Cookie池管理器演示")
    print("="*60)
    
    print("1. 创建Cookie池管理器...")
    manager = create_pool_manager(
        num_threads=2,
        batch_size=50,
        use_proxy=False
    )
    
    print("2. 显示管理器状态...")
    manager.print_status_report()
    
    print("3. 演示管理器功能...")
    
    # 监控池健康状态
    print("   监控Cookie池健康状态...")
    manager.monitor_pool_health()
    
    # 清理失效Cookie
    print("   清理失效Cookie...")
    manager.cleanup_failed_cookies()
    
    print("\n✓ 演示完成")


def interactive_demo():
    """交互式演示"""
    print("\n" + "="*60)
    print("Cookie池交互式演示")
    print("="*60)
    
    spider = PoolBasedGoogleSpider(use_cookie_pool=True)
    
    try:
        while True:
            print("\n可用操作:")
            print("1. 显示Cookie池状态")
            print("2. 执行搜索测试")
            print("3. 模拟Cookie失败")
            print("4. 刷新Cookie池")
            print("5. 清理失效Cookie")
            print("0. 退出")
            
            choice = input("\n请选择操作 (0-5): ").strip()
            
            if choice == '0':
                break
            elif choice == '1':
                status = spider.get_pool_status()
                print(f"\nCookie池状态: {status['active_cookies']}/{status['total_cookies']} 活跃")
                for cookie in status['cookies_detail']:
                    print(f"  {cookie['id']}: {cookie['status']} (失败{cookie['failure_count']}次)")
            
            elif choice == '2':
                keyword = input("请输入搜索关键词: ").strip()
                if keyword:
                    print(f"搜索: {keyword}")
                    result = spider.search_page(keyword, page=1)
                    if result['results']:
                        print(f"成功获取 {len(result['results'])} 个结果")
                        if result.get('cookie_id'):
                            print(f"使用Cookie: {result['cookie_id'][:8]}...")
                    else:
                        print("搜索失败或无结果")
            
            elif choice == '3':
                cookie = spider.cookie_pool.get_available_cookie()
                if cookie:
                    spider.cookie_pool.mark_cookie_failed(cookie.id)
                    print(f"标记Cookie {cookie.id[:8]}... 失败")
                else:
                    print("没有可用的Cookie")
            
            elif choice == '4':
                print("刷新Cookie池...")
                spider.refresh_cookie_pool()
                print("刷新完成")
            
            elif choice == '5':
                print("清理失效Cookie...")
                spider.cookie_pool.cleanup_failed_cookies()
                print("清理完成")
            
            else:
                print("无效选择，请重试")
    
    finally:
        spider.close()


def main():
    """主函数"""
    print("Cookie池功能演示")
    print("时间:", time.strftime("%Y-%m-%d %H:%M:%S"))
    
    if len(sys.argv) > 1:
        mode = sys.argv[1].lower()
        
        if mode == "basic":
            demo_basic_usage()
        elif mode == "failure":
            demo_failure_recovery()
        elif mode == "config":
            demo_configuration()
        elif mode == "manager":
            demo_manager()
        elif mode == "interactive":
            interactive_demo()
        else:
            print(f"未知模式: {mode}")
            print("可用模式: basic, failure, config, manager, interactive")
    else:
        # 运行所有演示
        try:
            demo_basic_usage()
            demo_failure_recovery()
            demo_configuration()
            demo_manager()
            
            print("\n" + "="*60)
            print("所有演示完成!")
            print("="*60)
            print("\n提示:")
            print("- 运行 'python run_cookie_pool_demo.py interactive' 进入交互模式")
            print("- 运行 'python run_cookie_pool_demo.py basic' 只看基本演示")
            print("- 运行 'python run_cookie_pool_demo.py failure' 只看失败恢复演示")
            
        except KeyboardInterrupt:
            print("\n\n演示被用户中断")
        except Exception as e:
            print(f"\n演示过程中出现异常: {e}")
            import traceback
            traceback.print_exc()


if __name__ == "__main__":
    main()
